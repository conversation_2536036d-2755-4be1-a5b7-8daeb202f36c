/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.16
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QWidget>
#include "src/BlinkenLight.h"

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *actionExit;
    QAction *actionAbout;
    QAction *actionPreferences;
    QWidget *centralWidget;
    QListWidget *lst_debug;
    QLabel *label_4;
    QLabel *label;
    QCheckBox *chk_on;
    QCheckBox *chk_debug;
    QComboBox *cmbMidiIn;
    QLabel *label_2;
    QLabel *label_3;
    QComboBox *cmbMidiOut;
    QComboBox *cmbSerial;
    BlinkenLight *led_serial;
    BlinkenLight *led_midiout;
    BlinkenLight *led_midiin;
    QMenuBar *menuBar;
    QMenu *menuFile;
    QMenu *menuHelp;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(604, 278);
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MainWindow->sizePolicy().hasHeightForWidth());
        MainWindow->setSizePolicy(sizePolicy);
        actionExit = new QAction(MainWindow);
        actionExit->setObjectName(QString::fromUtf8("actionExit"));
        actionAbout = new QAction(MainWindow);
        actionAbout->setObjectName(QString::fromUtf8("actionAbout"));
        actionPreferences = new QAction(MainWindow);
        actionPreferences->setObjectName(QString::fromUtf8("actionPreferences"));
        centralWidget = new QWidget(MainWindow);
        centralWidget->setObjectName(QString::fromUtf8("centralWidget"));
        lst_debug = new QListWidget(centralWidget);
        lst_debug->setObjectName(QString::fromUtf8("lst_debug"));
        lst_debug->setGeometry(QRect(10, 162, 591, 81));
        QSizePolicy sizePolicy1(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(1);
        sizePolicy1.setHeightForWidth(lst_debug->sizePolicy().hasHeightForWidth());
        lst_debug->setSizePolicy(sizePolicy1);
        lst_debug->setAutoScroll(true);
        label_4 = new QLabel(centralWidget);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setGeometry(QRect(210, 30, 201, 91));
        label_4->setPixmap(QPixmap(QString::fromUtf8(":/images/images/arrows.png")));
        label = new QLabel(centralWidget);
        label->setObjectName(QString::fromUtf8("label"));
        label->setGeometry(QRect(10, 50, 146, 17));
        label->setMaximumSize(QSize(212, 16777215));
        chk_on = new QCheckBox(centralWidget);
        chk_on->setObjectName(QString::fromUtf8("chk_on"));
        chk_on->setGeometry(QRect(10, 10, 185, 22));
        chk_on->setChecked(true);
        chk_debug = new QCheckBox(centralWidget);
        chk_debug->setObjectName(QString::fromUtf8("chk_debug"));
        chk_debug->setGeometry(QRect(10, 130, 201, 22));
        cmbMidiIn = new QComboBox(centralWidget);
        cmbMidiIn->setObjectName(QString::fromUtf8("cmbMidiIn"));
        cmbMidiIn->setGeometry(QRect(430, 100, 170, 27));
        label_2 = new QLabel(centralWidget);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setGeometry(QRect(430, 20, 175, 17));
        label_2->setMaximumSize(QSize(212, 16777215));
        label_3 = new QLabel(centralWidget);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setGeometry(QRect(430, 80, 149, 17));
        label_3->setMaximumSize(QSize(212, 16777215));
        cmbMidiOut = new QComboBox(centralWidget);
        cmbMidiOut->setObjectName(QString::fromUtf8("cmbMidiOut"));
        cmbMidiOut->setGeometry(QRect(430, 40, 170, 27));
        cmbSerial = new QComboBox(centralWidget);
        cmbSerial->setObjectName(QString::fromUtf8("cmbSerial"));
        cmbSerial->setGeometry(QRect(10, 71, 170, 27));
        led_serial = new BlinkenLight(centralWidget);
        led_serial->setObjectName(QString::fromUtf8("led_serial"));
        led_serial->setGeometry(QRect(185, 77, 16, 16));
        led_serial->setPixmap(QPixmap(QString::fromUtf8(":/images/images/led-off.png")));
        led_midiout = new BlinkenLight(centralWidget);
        led_midiout->setObjectName(QString::fromUtf8("led_midiout"));
        led_midiout->setGeometry(QRect(410, 45, 16, 16));
        led_midiout->setPixmap(QPixmap(QString::fromUtf8(":/images/images/led-off.png")));
        led_midiin = new BlinkenLight(centralWidget);
        led_midiin->setObjectName(QString::fromUtf8("led_midiin"));
        led_midiin->setGeometry(QRect(410, 105, 16, 16));
        led_midiin->setPixmap(QPixmap(QString::fromUtf8(":/images/images/led-off.png")));
        MainWindow->setCentralWidget(centralWidget);
        menuBar = new QMenuBar(MainWindow);
        menuBar->setObjectName(QString::fromUtf8("menuBar"));
        menuBar->setGeometry(QRect(0, 0, 604, 26));
        menuFile = new QMenu(menuBar);
        menuFile->setObjectName(QString::fromUtf8("menuFile"));
        menuHelp = new QMenu(menuBar);
        menuHelp->setObjectName(QString::fromUtf8("menuHelp"));
        MainWindow->setMenuBar(menuBar);
        QWidget::setTabOrder(chk_on, cmbSerial);
        QWidget::setTabOrder(cmbSerial, cmbMidiOut);
        QWidget::setTabOrder(cmbMidiOut, cmbMidiIn);
        QWidget::setTabOrder(cmbMidiIn, chk_debug);
        QWidget::setTabOrder(chk_debug, lst_debug);

        menuBar->addAction(menuFile->menuAction());
        menuBar->addAction(menuHelp->menuAction());
        menuFile->addAction(actionPreferences);
        menuFile->addSeparator();
        menuFile->addAction(actionExit);
        menuHelp->addAction(actionAbout);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "Hairless MIDI<->Serial Bridge", nullptr));
        actionExit->setText(QCoreApplication::translate("MainWindow", "Exit", nullptr));
#if QT_CONFIG(shortcut)
        actionExit->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Q", nullptr));
#endif // QT_CONFIG(shortcut)
        actionAbout->setText(QCoreApplication::translate("MainWindow", "About Hairless MIDI<->Serial Bridge...", nullptr));
        actionPreferences->setText(QCoreApplication::translate("MainWindow", "Preferences...", nullptr));
        label_4->setText(QString());
        label->setText(QCoreApplication::translate("MainWindow", "Serial port", nullptr));
        chk_on->setText(QCoreApplication::translate("MainWindow", "Serial<->MIDI Bridge On", nullptr));
        chk_debug->setText(QCoreApplication::translate("MainWindow", "Debug MIDI messages", nullptr));
        label_2->setText(QCoreApplication::translate("MainWindow", "MIDI Out", nullptr));
        label_3->setText(QCoreApplication::translate("MainWindow", "MIDI In", nullptr));
        led_serial->setText(QString());
        led_midiout->setText(QString());
        led_midiin->setText(QString());
        menuFile->setTitle(QCoreApplication::translate("MainWindow", "File", nullptr));
        menuHelp->setTitle(QCoreApplication::translate("MainWindow", "Help", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
