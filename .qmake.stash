QMAKE_MAC_SDK.macosx.Path = /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
QMAKE_MAC_SDK.macosx.SDKVersion = 13.3
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_CC = /Library/Developer/CommandLineTools/usr/bin/clang
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_CXX = /Library/Developer/CommandLineTools/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_FIX_RPATH = \
    /Library/Developer/CommandLineTools/usr/bin/install_name_tool \
    -id
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_AR = \
    /Library/Developer/CommandLineTools/usr/bin/ar \
    cq
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_RANLIB = \
    /Library/Developer/CommandLineTools/usr/bin/ranlib \
    -s
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK = /Library/Developer/CommandLineTools/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_SHLIB = /Library/Developer/CommandLineTools/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_C = /Library/Developer/CommandLineTools/usr/bin/clang
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_C_SHLIB = /Library/Developer/CommandLineTools/usr/bin/clang
QMAKE_CXX.QT_COMPILER_STDCXX = 199711L
QMAKE_CXX.QMAKE_APPLE_CC = 6000
QMAKE_CXX.QMAKE_APPLE_CLANG_MAJOR_VERSION = 14
QMAKE_CXX.QMAKE_APPLE_CLANG_MINOR_VERSION = 0
QMAKE_CXX.QMAKE_APPLE_CLANG_PATCH_VERSION = 3
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 4
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 1
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_APPLE_CC \
    QMAKE_APPLE_CLANG_MAJOR_VERSION \
    QMAKE_APPLE_CLANG_MINOR_VERSION \
    QMAKE_APPLE_CLANG_PATCH_VERSION \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 \
    /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include \
    /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include \
    /Library/Developer/CommandLineTools/usr/include
QMAKE_CXX.LIBDIRS = /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib
QMAKE_MAC_SDK.macosx.SDKVersion = 13.3
QMAKE_MAC_SDK.macosx.Path = /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
QMAKE_XCODE_DEVELOPER_PATH = /Library/Developer/CommandLineTools
