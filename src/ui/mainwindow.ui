<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>604</width>
    <height>278</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Hairless MIDI&lt;-&gt;Serial Bridge</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <widget class="QListWidget" name="lst_debug">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>162</y>
      <width>591</width>
      <height>81</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
      <horstretch>0</horstretch>
      <verstretch>1</verstretch>
     </sizepolicy>
    </property>
    <property name="autoScroll">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_4">
    <property name="geometry">
     <rect>
      <x>210</x>
      <y>30</y>
      <width>201</width>
      <height>91</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/images/arrows.png</pixmap>
    </property>
   </widget>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>50</y>
      <width>146</width>
      <height>17</height>
     </rect>
    </property>
    <property name="maximumSize">
     <size>
      <width>212</width>
      <height>16777215</height>
     </size>
    </property>
    <property name="text">
     <string>Serial port</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="chk_on">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>185</width>
      <height>22</height>
     </rect>
    </property>
    <property name="text">
     <string>Serial&lt;-&gt;MIDI Bridge On</string>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QCheckBox" name="chk_debug">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>130</y>
      <width>201</width>
      <height>22</height>
     </rect>
    </property>
    <property name="text">
     <string>Debug MIDI messages</string>
    </property>
   </widget>
   <widget class="QComboBox" name="cmbMidiIn">
    <property name="geometry">
     <rect>
      <x>430</x>
      <y>100</y>
      <width>170</width>
      <height>27</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>430</x>
      <y>20</y>
      <width>175</width>
      <height>17</height>
     </rect>
    </property>
    <property name="maximumSize">
     <size>
      <width>212</width>
      <height>16777215</height>
     </size>
    </property>
    <property name="text">
     <string>MIDI Out</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_3">
    <property name="geometry">
     <rect>
      <x>430</x>
      <y>80</y>
      <width>149</width>
      <height>17</height>
     </rect>
    </property>
    <property name="maximumSize">
     <size>
      <width>212</width>
      <height>16777215</height>
     </size>
    </property>
    <property name="text">
     <string>MIDI In</string>
    </property>
   </widget>
   <widget class="QComboBox" name="cmbMidiOut">
    <property name="geometry">
     <rect>
      <x>430</x>
      <y>40</y>
      <width>170</width>
      <height>27</height>
     </rect>
    </property>
   </widget>
   <widget class="QComboBox" name="cmbSerial">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>71</y>
      <width>170</width>
      <height>27</height>
     </rect>
    </property>
   </widget>
   <widget class="BlinkenLight" name="led_serial">
    <property name="geometry">
     <rect>
      <x>185</x>
      <y>77</y>
      <width>16</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/images/led-off.png</pixmap>
    </property>
   </widget>
   <widget class="BlinkenLight" name="led_midiout">
    <property name="geometry">
     <rect>
      <x>410</x>
      <y>45</y>
      <width>16</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/images/led-off.png</pixmap>
    </property>
   </widget>
   <widget class="BlinkenLight" name="led_midiin">
    <property name="geometry">
     <rect>
      <x>410</x>
      <y>105</y>
      <width>16</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/images/led-off.png</pixmap>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>604</width>
     <height>26</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionPreferences"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuHelp"/>
  </widget>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About Hairless MIDI&lt;-&gt;Serial Bridge...</string>
   </property>
  </action>
  <action name="actionPreferences">
   <property name="text">
    <string>Preferences...</string>
   </property>
  </action>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>BlinkenLight</class>
   <extends>QLabel</extends>
   <header>src/BlinkenLight.h</header>
  </customwidget>
 </customwidgets>
 <tabstops>
  <tabstop>chk_on</tabstop>
  <tabstop>cmbSerial</tabstop>
  <tabstop>cmbMidiOut</tabstop>
  <tabstop>cmbMidiIn</tabstop>
  <tabstop>chk_debug</tabstop>
  <tabstop>lst_debug</tabstop>
 </tabstops>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
