<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SettingsDialog</class>
 <widget class="QDialog" name="SettingsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>379</width>
    <height>403</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Settings</string>
  </property>
  <widget class="QDialogButtonBox" name="buttonBox">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>360</y>
     <width>341</width>
     <height>32</height>
    </rect>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
   <property name="standardButtons">
    <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
   </property>
  </widget>
  <widget class="QGroupBox" name="groupBox">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>10</y>
     <width>331</width>
     <height>241</height>
    </rect>
   </property>
   <property name="title">
    <string>Serial Port Settings</string>
   </property>
   <widget class="QWidget" name="formLayoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>311</width>
      <height>181</height>
     </rect>
    </property>
    <layout class="QFormLayout" name="formLayout">
     <property name="fieldGrowthPolicy">
      <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="baudRateLabel">
       <property name="text">
        <string>Baud rate</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QComboBox" name="cmb_baud"/>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="dataBitsLabel">
       <property name="text">
        <string>Data Bits</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QComboBox" name="cmb_dataBits"/>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="parityLabel">
       <property name="text">
        <string>Parity</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QComboBox" name="cmb_parity"/>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="stopBitSLabel">
       <property name="text">
        <string>Stop Bit(s)</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QComboBox" name="cmb_stop"/>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="flowControlLabel">
       <property name="text">
        <string>Flow Control</string>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="QComboBox" name="cmb_flow"/>
     </item>
    </layout>
   </widget>
   <widget class="QPushButton" name="btn_defaults">
    <property name="geometry">
     <rect>
      <x>172</x>
      <y>210</y>
      <width>141</width>
      <height>28</height>
     </rect>
    </property>
    <property name="text">
     <string>Restore Defaults</string>
    </property>
   </widget>
  </widget>
  <widget class="QGroupBox" name="groupBox_2">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>270</y>
     <width>331</width>
     <height>71</height>
    </rect>
   </property>
   <property name="title">
    <string>Debugging Output</string>
   </property>
   <widget class="QWidget" name="formLayoutWidget_2">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>20</y>
      <width>321</width>
      <height>31</height>
     </rect>
    </property>
    <layout class="QFormLayout" name="formLayout_2">
     <property name="fieldGrowthPolicy">
      <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="linesOfDebugScrollbackLabel">
       <property name="text">
        <string>Lines of Debug Scrollback</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QSpinBox" name="spn_scrollback">
       <property name="minimum">
        <number>2</number>
       </property>
       <property name="maximum">
        <number>2000</number>
       </property>
       <property name="singleStep">
        <number>10</number>
       </property>
       <property name="value">
        <number>50</number>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
 </widget>
 <tabstops>
  <tabstop>cmb_baud</tabstop>
  <tabstop>cmb_dataBits</tabstop>
  <tabstop>cmb_parity</tabstop>
  <tabstop>cmb_stop</tabstop>
  <tabstop>cmb_flow</tabstop>
  <tabstop>btn_defaults</tabstop>
  <tabstop>spn_scrollback</tabstop>
  <tabstop>buttonBox</tabstop>
 </tabstops>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>SettingsDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>238</x>
     <y>334</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>SettingsDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>306</x>
     <y>340</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>SettingsDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>242</x>
     <y>348</y>
    </hint>
    <hint type="destinationlabel">
     <x>259</x>
     <y>318</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
