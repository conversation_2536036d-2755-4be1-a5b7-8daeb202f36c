-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Process:               hairless-midiserial [3102]
Path:                  /Users/<USER>/*/hairless-midiserial.app/Contents/MacOS/hairless-midiserial
Identifier:            777.hairless-midiserial
Version:               ???
Code Type:             X86-64 (Native)
Parent Process:        launchd [1]
User ID:               501

Date/Time:             2025-06-30 06:53:57.0267 +0200
OS Version:            macOS 13.7.5 (22H527)
Report Version:        12
Anonymous UUID:        6CF1AB2C-D1A3-4A56-CDA3-D91B77E7DFAA


Time Awake Since Boot: 1700 seconds

System Integrity Protection: enabled

Crashed Thread:        0  Dispatch queue: com.apple.main-thread

Exception Type:        EXC_CRASH (SIGABRT)
Exception Codes:       0x0000000000000000, 0x0000000000000000

Termination Reason:    Namespace SIGNAL, Code 6 Abort trap: 6
Terminating Process:   hairless-midiserial [3102]

Application Specific Information:
abort() called


Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libsystem_kernel.dylib        	    0x7ff80ebfd196 __pthread_kill + 10
1   libsystem_pthread.dylib       	    0x7ff80ec34ee6 pthread_kill + 263
2   libsystem_c.dylib             	    0x7ff80eb5bb45 abort + 123
3   libc++abi.dylib               	    0x7ff80ebef282 abort_message + 241
4   libc++abi.dylib               	    0x7ff80ebe13e1 demangling_terminate_handler() + 241
5   libobjc.A.dylib               	    0x7ff80e8b4a6f _objc_terminate() + 104
6   libc++abi.dylib               	    0x7ff80ebee6db std::__terminate(void (*)()) + 6
7   libc++abi.dylib               	    0x7ff80ebf1123 __cxa_rethrow + 99
8   libobjc.A.dylib               	    0x7ff80e8c09f5 objc_exception_rethrow + 37
9   AppKit                        	    0x7ff811cfef40 -[NSApplication run] + 659
10  libqcocoa.dylib               	       0x1fed89244 0x1fed4b000 + 254532
11  QtCore                        	       0x10977b4a6 QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>) + 502
12  QtCore                        	       0x10977ef23 QCoreApplication::exec() + 131
13  hairless-midiserial           	       0x107d42f86 main + 310
14  dyld                          	    0x7ff80e8da418 start + 1896

Thread 1:: QThread
0   libsystem_kernel.dylib        	    0x7ff80ebfd242 poll + 10
1   QtCore                        	       0x1097d9d8d qt_safe_poll(pollfd*, unsigned int, timespec const*) + 77
2   QtCore                        	       0x1097db5ff QEventDispatcherUNIX::processEvents(QFlags<QEventLoop::ProcessEventsFlag>) + 815
3   QtCore                        	       0x10977b4a6 QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>) + 502
4   QtCore                        	       0x1095cb8bb QThread::exec() + 139
5   QtCore                        	       0x1095cc793 0x1095ab000 + 137107
6   libsystem_pthread.dylib       	    0x7ff80ec351d3 _pthread_start + 125
7   libsystem_pthread.dylib       	    0x7ff80ec30bd3 thread_start + 15

Thread 2:
0   libsystem_kernel.dylib        	    0x7ff80ebf6552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	    0x7ff80ec046cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	    0x7ff80ebfd584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	    0x7ff80ebf683a mach_msg + 19
4   CoreMIDI                      	    0x7ff828567d50 XServerMachPort::ReceiveMessage(int&, void*, int&) + 94
5   CoreMIDI                      	    0x7ff8285998c5 MIDIProcess::MIDIInPortThread::Run() + 105
6   CoreMIDI                      	    0x7ff828581c44 CADeprecated::XThread::RunHelper(void*) + 10
7   CoreMIDI                      	    0x7ff828582e9f CADeprecated::CAPThread::Entry(CADeprecated::CAPThread*) + 77
8   libsystem_pthread.dylib       	    0x7ff80ec351d3 _pthread_start + 125
9   libsystem_pthread.dylib       	    0x7ff80ec30bd3 thread_start + 15

Thread 3:: caulk.messenger.shared:17
0   libsystem_kernel.dylib        	    0x7ff80ebf64ce semaphore_wait_trap + 10
1   caulk                         	    0x7ff81899b07e caulk::semaphore::timed_wait(double) + 150
2   caulk                         	    0x7ff81899af9c caulk::concurrent::details::worker_thread::run() + 30
3   caulk                         	    0x7ff81899acb0 void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*) + 41
4   libsystem_pthread.dylib       	    0x7ff80ec351d3 _pthread_start + 125
5   libsystem_pthread.dylib       	    0x7ff80ec30bd3 thread_start + 15

Thread 4:: com.apple.NSEventThread
0   libsystem_kernel.dylib        	    0x7ff80ebf6552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	    0x7ff80ec046cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	    0x7ff80ebfd584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	    0x7ff80ebf683a mach_msg + 19
4   CoreFoundation                	    0x7ff80ed10940 __CFRunLoopServiceMachPort + 145
5   CoreFoundation                	    0x7ff80ed0f3cb __CFRunLoopRun + 1365
6   CoreFoundation                	    0x7ff80ed0e81c CFRunLoopRunSpecific + 560
7   AppKit                        	    0x7ff811e6dc25 _NSEventThread + 132
8   libsystem_pthread.dylib       	    0x7ff80ec351d3 _pthread_start + 125
9   libsystem_pthread.dylib       	    0x7ff80ec30bd3 thread_start + 15

Thread 5:
0   libsystem_pthread.dylib       	    0x7ff80ec30bb0 start_wqthread + 0

Thread 6:
0   libsystem_pthread.dylib       	    0x7ff80ec30bb0 start_wqthread + 0

Thread 7:
0   libsystem_pthread.dylib       	    0x7ff80ec30bb0 start_wqthread + 0

Thread 8:
0   libsystem_pthread.dylib       	    0x7ff80ec30bb0 start_wqthread + 0


Thread 0 crashed with X86 Thread State (64-bit):
  rax: 0x0000000000000000  rbx: 0x00007ff852312c80  rcx: 0x00007ff7b81c33a8  rdx: 0x0000000000000000
  rdi: 0x0000000000000103  rsi: 0x0000000000000006  rbp: 0x00007ff7b81c33d0  rsp: 0x00007ff7b81c33a8
   r8: 0x00007ff7b81c3270   r9: 0x00007ff80ebf26fe  r10: 0x0000000000000000  r11: 0x0000000000000246
  r12: 0x0000000000000103  r13: 0x0000003000000008  r14: 0x0000000000000006  r15: 0x0000000000000016
  rip: 0x00007ff80ebfd196  rfl: 0x0000000000000246  cr2: 0x0000000000000000
  
Logical CPU:     0
Error Code:      0x02000148 
Trap Number:     133


Binary Images:
       0x107d3c000 -        0x107d67fff 777.hairless-midiserial (*) <6eb2dae4-d68d-301b-b935-553e7de38838> /Users/<USER>/*/hairless-midiserial.app/Contents/MacOS/hairless-midiserial
       0x108369000 -        0x1087a4fff org.qt-project.QtWidgets (5.15) <89354b8b-2ec2-337f-9989-f8e24e66ec31> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Versions/5/QtWidgets
       0x108f58000 -        0x109493fff org.qt-project.QtGui (5.15) <f6eaf280-a465-3cac-892d-73331684638c> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Versions/5/QtGui
       0x108909000 -        0x108ae8fff com.apple.Metal (306.7.5) <6581cd53-8583-38d1-b4b9-773f1dc3f551> /System/Library/Frameworks/Metal.framework/Versions/A/Metal
       0x1095ab000 -        0x109a32fff org.qt-project.QtCore (5.15) <a04c0588-4524-369a-8473-010d2cd62467> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Versions/5/QtCore
       0x109afd000 -        0x109b44fff libllvm-flatbuffers.dylib (*) <d5cf0007-b92a-3d76-9549-1fb7bc428091> /System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/31001/Libraries/libllvm-flatbuffers.dylib
       0x1835a1000 -        0x1835a4fff libGPUCompilerUtils.dylib (*) <d5cf0007-b778-37c5-917b-186e88376470> /System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/31001/Libraries/libGPUCompilerUtils.dylib
       0x107df8000 -        0x107e1bfff libpng16.16.dylib (*) <bcab46dd-fe50-3b76-a9e4-4ef840aa2bd2> /usr/local/Cellar/libpng/1.6.49/lib/libpng16.16.dylib
       0x107dda000 -        0x107de5fff libmd4c.0.5.2.dylib (*) <a8976492-67b2-30f9-9009-b6edc2720565> /usr/local/Cellar/md4c/0.5.2/lib/libmd4c.0.5.2.dylib
       0x107eb9000 -        0x107f3cfff libpcre2-16.0.dylib (*) <1f031d9d-8dcc-3569-bddd-76324638905e> /usr/local/Cellar/pcre2/10.45/lib/libpcre2-16.0.dylib
       0x108008000 -        0x1080affff libzstd.1.5.7.dylib (*) <c3d59a35-9312-3324-b1d5-2d05656b0740> /usr/local/Cellar/zstd/1.5.7/lib/libzstd.1.5.7.dylib
       0x107e28000 -        0x107e2bfff libgthread-2.0.0.dylib (*) <db3732fe-b897-3594-97f8-8f973dba11b6> /usr/local/Cellar/glib/2.84.3/lib/libgthread-2.0.0.dylib
       0x1081eb000 -        0x1082defff libglib-2.0.0.dylib (*) <0eb569ed-28cf-3524-90d6-5094614a570c> /usr/local/Cellar/glib/2.84.3/lib/libglib-2.0.0.dylib
       0x107e6c000 -        0x107e97fff libintl.8.dylib (*) <63ab731a-7beb-3204-a38b-b360a4101509> /usr/local/Cellar/gettext/0.25/lib/libintl.8.dylib
       0x1080c8000 -        0x108153fff libpcre2-8.0.dylib (*) <16d2ea89-5eb5-34f5-9e6a-c13634bb0686> /usr/local/Cellar/pcre2/10.45/lib/libpcre2-8.0.dylib
       0x1fed4b000 -        0x1fedfafff libqcocoa.dylib (*) <5e4bec31-5b7d-3d92-82e9-d06fed2c7b83> /usr/local/Cellar/qt@5/5.15.16_2/plugins/platforms/libqcocoa.dylib
       0x108e21000 -        0x108e80fff org.qt-project.QtDBus (5.15) <b221a0bd-8567-30bb-b7c7-a9a8363d2a7a> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtDBus.framework/Versions/5/QtDBus
       0x1fee45000 -        0x1feec4fff libfreetype.6.dylib (*) <f3485bec-6ae1-300e-bab1-fa3187b0c01e> /usr/local/Cellar/freetype/2.13.3/lib/libfreetype.6.dylib
       0x10830f000 -        0x10833afff org.qt-project.QtPrintSupport (5.15) <e71b8452-02a7-3c4a-8495-40d025e480bb> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport
       0x201c08000 -        0x201c2bfff libqmacstyle.dylib (*) <f474f057-6aaf-3787-bf0d-227b68a12b4b> /usr/local/Cellar/qt@5/5.15.16_2/plugins/styles/libqmacstyle.dylib
       0x108f29000 -        0x108f30fff libqgif.dylib (*) <1f91cd9e-6d02-3341-8bd8-9b5779eb5c3b> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqgif.dylib
       0x108f46000 -        0x108f4dfff libqicns.dylib (*) <f303c9fe-ab3d-31ce-a97e-7b6ddcf7a7df> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqicns.dylib
       0x108f37000 -        0x108f3efff libqico.dylib (*) <eca4e00c-4a55-3383-b6d7-eebe3f62a59f> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqico.dylib
       0x201c4d000 -        0x201c54fff libqjpeg.dylib (*) <6bd02a21-d32d-3779-a240-083fa0adfd99> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqjpeg.dylib
       0x201d07000 -        0x201d92fff libjpeg.8.3.2.dylib (*) <f4be5d71-e90a-3ca5-b72f-5ba6f64da288> /usr/local/Cellar/jpeg-turbo/3.1.1/lib/libjpeg.8.3.2.dylib
       0x201c5d000 -        0x201c64fff libqmacheif.dylib (*) <271ac1ef-0820-33ad-b461-a2c009d3295b> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqmacheif.dylib
       0x201c6c000 -        0x201c73fff libqmacjp2.dylib (*) <8d995200-407e-3f45-858d-16830ea90978> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqmacjp2.dylib
       0x201c3d000 -        0x201c44fff libqpdf.dylib (*) <0dd18838-486e-3866-be7d-783ccaec8a15> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqpdf.dylib
       0x2022ee000 -        0x2027fdfff org.qt-project.QtPdf (5.15) <1f6e5c3f-e220-3a15-a432-7f01fd566986> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtPdf.framework/Versions/5/QtPdf
       0x201f06000 -        0x20200dfff org.qt-project.QtNetwork (5.15) <7ff81240-**************-47ebb7cd8b62> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Versions/5/QtNetwork
       0x201c8a000 -        0x201c91fff libqsvg.dylib (*) <67a0157a-a0e1-331e-8797-46b1e5aac642> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqsvg.dylib
       0x201db3000 -        0x201deafff org.qt-project.QtSvg (5.15) <bbe7a2b9-b177-3b40-9814-8789ffb64556> /usr/local/Cellar/qt@5/5.15.16_2/lib/QtSvg.framework/Versions/5/QtSvg
       0x201c7b000 -        0x201c82fff libqtga.dylib (*) <1081f6a5-0d4f-38cc-9929-a2241b810fad> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqtga.dylib
       0x201cac000 -        0x201cb7fff libqtiff.dylib (*) <d5d50065-619d-3bd1-9b73-c6b7f56314f5> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqtiff.dylib
       0x202059000 -        0x2020c4fff libtiff.6.dylib (*) <0d8cd49b-491f-31ac-b8e3-2ad90db1f6e5> /usr/local/Cellar/libtiff/4.7.0/lib/libtiff.6.dylib
       0x201e08000 -        0x201e27fff liblzma.5.dylib (*) <79cca5fb-e119-3c0d-92bc-d607699fcdd3> /usr/local/Cellar/xz/5.8.1/lib/liblzma.5.dylib
       0x201c99000 -        0x201ca0fff libqwbmp.dylib (*) <1446f187-0685-36e9-b099-df0564b08b27> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqwbmp.dylib
       0x201cce000 -        0x201cd5fff libqwebp.dylib (*) <780b4417-bdf7-31cf-87c0-ae9d10367576> /usr/local/Cellar/qt@5/5.15.16_2/plugins/imageformats/libqwebp.dylib
       0x201cdd000 -        0x201ce4fff libwebpmux.3.1.1.dylib (*) <6a64dc7b-3963-3ec6-9d17-ece06ff0b2d8> /usr/local/Cellar/webp/1.5.0/lib/libwebpmux.3.1.1.dylib
       0x201cbf000 -        0x201cc2fff libwebpdemux.2.0.16.dylib (*) <b23245cd-894f-3fde-96cb-a9362d1b83b8> /usr/local/Cellar/webp/1.5.0/lib/libwebpdemux.2.0.16.dylib
       0x2020dd000 -        0x202134fff libwebp.7.1.10.dylib (*) <78804bac-4bf8-34fd-8a5d-6fb769c14779> /usr/local/Cellar/webp/1.5.0/lib/libwebp.7.1.10.dylib
       0x201ced000 -        0x201cf0fff libsharpyuv.0.1.1.dylib (*) <7b64a3c3-1381-3860-89ed-c41f7a7bbe8c> /usr/local/Cellar/webp/1.5.0/lib/libsharpyuv.0.1.1.dylib
       0x201e79000 -        0x201e88fff libobjc-trampolines.dylib (*) <3f9b23a1-7826-30b9-b791-27c612efa764> /usr/lib/libobjc-trampolines.dylib
       0x2021c8000 -        0x2021cffff com.apple.audio.AppleHDAHALPlugIn (440.2) <01ffcedc-b862-3154-9715-ff565fd4b625> /System/Library/Extensions/AppleHDA.kext/Contents/PlugIns/AppleHDAHALPlugIn.bundle/Contents/MacOS/AppleHDAHALPlugIn
       0x2022d7000 -        0x2022d8fff com.apple.driver.AppleIntelHD4000GraphicsMTLDriver (16.5.14) <4aca8344-1dbd-368b-a489-f3bff99ae117> /System/Library/Extensions/AppleIntelHD4000GraphicsMTLDriver.bundle/Contents/MacOS/AppleIntelHD4000GraphicsMTLDriver
       0x202e8f000 -        0x202ef4fff AppleIntelHD4000GraphicsMTLDriver_real (*) <d5ce0008-cae6-34e9-90e3-7f28f989a2cb> /System/Library/Extensions/AppleIntelHD4000GraphicsMTLDriver.bundle/Contents/MacOS/AppleIntelHD4000GraphicsMTLDriver_real
       0x25fa51000 -        0x25fc10fff com.apple.GeForceMTLDriver (16.0.12) <dc9e7ee8-2b92-3fc3-9524-fcae13d2f69d> /System/Library/Extensions/GeForceMTLDriver.bundle/Contents/MacOS/GeForceMTLDriver
       0x202d4f000 -        0x202d52fff Shim.dylib (*) <a8c8d7c3-9a85-391b-a3f3-430a2bf54b91> /System/Library/Extensions/GeForceMTLDriver.bundle/Contents/MacOS/Shim.dylib
    0x7ff80ebf5000 -     0x7ff80ec2efff libsystem_kernel.dylib (*) <69e3eb4e-fb58-3fde-a3f0-4f1a5ddcd2d1> /usr/lib/system/libsystem_kernel.dylib
    0x7ff80ec2f000 -     0x7ff80ec3afff libsystem_pthread.dylib (*) <51a43b06-feb4-3836-9e4f-21b69bb13726> /usr/lib/system/libsystem_pthread.dylib
    0x7ff80eadc000 -     0x7ff80eb63ff7 libsystem_c.dylib (*) <77738df4-82b8-3690-937e-cc1d4981a630> /usr/lib/system/libsystem_c.dylib
    0x7ff80ebe0000 -     0x7ff80ebf4ff3 libc++abi.dylib (*) <717bce6c-23b4-31dc-9d8b-6859639a38d5> /usr/lib/libc++abi.dylib
    0x7ff80e899000 -     0x7ff80e8d3fc0 libobjc.A.dylib (*) <dd8ed00f-eb16-3edc-8a53-1916f5e82556> /usr/lib/libobjc.A.dylib
    0x7ff811ccf000 -     0x7ff812cd8ff7 com.apple.AppKit (6.9) <5ad23f03-65ba-3aa0-a14e-14b45615ded3> /System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
    0x7ff80e8d4000 -     0x7ff80e96c62f dyld (*) <5baa56eb-4369-3aeb-9aa7-f973e58d9b8e> /usr/lib/dyld
    0x7ff828556000 -     0x7ff8285c3ffd com.apple.audio.midi.CoreMIDI (2.0) <7320de0b-4b1b-35a6-8190-1946262bec3d> /System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
    0x7ff818999000 -     0x7ff8189bdff3 com.apple.audio.caulk (1.0) <4d56b5eb-40ec-3980-8272-018c6373a3c1> /System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
    0x7ff80ec94000 -     0x7ff80f12efff com.apple.CoreFoundation (6.9) <c21f327f-e547-3cf0-9a91-721ab86fe6e6> /System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0

VM Region Summary:
ReadOnly portion of Libraries: Total=481.1M resident=0K(0%) swapped_out_or_unallocated=481.1M(100%)
Writable regions: Total=5.9G written=0K(0%) resident=0K(0%) swapped_out=0K(0%) unallocated=5.9G(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Accelerate framework               128K        1 
Activity Tracing                   256K        1 
CG backing stores                 2160K        4 
CG image                            64K        5 
ColorSync                          264K       29 
CoreAnimation                       68K        7 
CoreGraphics                        20K        3 
CoreUI image data                 1936K       13 
Foundation                          16K        1 
Kernel Alloc Once                    8K        1 
MALLOC                           226.8M       64 
MALLOC guard page                   32K        7 
MALLOC_NANO (reserved)           384.0M        1         reserved VM address space (unallocated)
STACK GUARD                       56.0M        9 
Stack                             12.1M        9 
VM_ALLOCATE                       97.7M       15 
VM_ALLOCATE (reserved)             5.2G        7         reserved VM address space (unallocated)
__CTF                               824        1 
__DATA                            20.2M      387 
__DATA_CONST                      16.0M      228 
__DATA_DIRTY                       736K      114 
__FONT_DATA                        2352        1 
__LINKEDIT                       178.7M       50 
__OBJC_RO                         66.3M        1 
__OBJC_RW                         2011K        2 
__TEXT                           302.4M      407 
dsce.bs.names                      112K        1 
dyld private memory                260K        2 
mapped file                       79.7M       27 
shared memory                      840K       26 
===========                     =======  ======= 
TOTAL                              6.6G     1424 
TOTAL, minus reserved VM space     1.0G     1424 



-----------
Full Report
-----------

{"app_name":"hairless-midiserial","timestamp":"2025-06-30 06:54:09.00 +0200","app_version":"","slice_uuid":"6eb2dae4-d68d-301b-b935-553e7de38838","build_version":"","platform":1,"bundleID":"777.hairless-midiserial","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 13.7.5 (22H527)","roots_installed":0,"name":"hairless-midiserial","incident_id":"05840990-0AE4-4599-8CC2-0C6AE6358CDF"}
{
  "uptime" : 1700,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro10,1",
  "coalitionID" : 1019,
  "osVersion" : {
    "train" : "macOS 13.7.5",
    "build" : "22H527",
    "releaseType" : "User"
  },
  "captureTime" : "2025-06-30 06:53:57.0267 +0200",
  "incident" : "05840990-0AE4-4599-8CC2-0C6AE6358CDF",
  "pid" : 3102,
  "cpuType" : "X86-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-06-30 06:53:18.4623 +0200",
  "procStartAbsTime" : 1668687186087,
  "procExitAbsTime" : 1707160920194,
  "procName" : "hairless-midiserial",
  "procPath" : "\/Users\/<USER>\/*\/hairless-midiserial.app\/Contents\/MacOS\/hairless-midiserial",
  "bundleInfo" : {"CFBundleIdentifier":"777.hairless-midiserial"},
  "storeInfo" : {"deviceIdentifierForVendor":"E38EB1E7-F086-5A63-B65A-FBCE283FBBF5","thirdParty":true},
  "parentProc" : "launchd",
  "parentPid" : 1,
  "coalitionName" : "777.hairless-midiserial",
  "crashReporterKey" : "6CF1AB2C-D1A3-4A56-CDA3-D91B77E7DFAA",
  "codeSigningID" : "",
  "codeSigningTeamID" : "",
  "codeSigningValidationCategory" : 0,
  "codeSigningTrustLevel" : 0,
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"hairless-midiserial","byPid":3102},
  "asi" : {"libsystem_c.dylib":["abort() called"]},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":24242,"threadState":{"r13":{"value":206158430216},"rax":{"value":0},"rflags":{"value":582},"cpu":{"value":0},"r14":{"value":6},"rsi":{"value":6},"r8":{"value":140701922505328},"cr2":{"value":0},"rdx":{"value":0},"r10":{"value":0},"r9":{"value":140703376025342},"r15":{"value":22},"rbx":{"value":140704507571328,"symbolLocation":0,"symbol":"_main_thread"},"trap":{"value":133},"err":{"value":33554760},"r11":{"value":582},"rip":{"value":140703376069014,"matchesCrashFrame":1},"rbp":{"value":140701922505680},"rsp":{"value":140701922505640},"r12":{"value":259},"rcx":{"value":140701922505640},"flavor":"x86_THREAD_STATE","rdi":{"value":259}},"queue":"com.apple.main-thread","frames":[{"imageOffset":33174,"symbol":"__pthread_kill","symbolLocation":10,"imageIndex":48},{"imageOffset":24294,"symbol":"pthread_kill","symbolLocation":263,"imageIndex":49},{"imageOffset":523077,"symbol":"abort","symbolLocation":123,"imageIndex":50},{"imageOffset":62082,"symbol":"abort_message","symbolLocation":241,"imageIndex":51},{"imageOffset":5089,"symbol":"demangling_terminate_handler()","symbolLocation":241,"imageIndex":51},{"imageOffset":113263,"symbol":"_objc_terminate()","symbolLocation":104,"imageIndex":52},{"imageOffset":59099,"symbol":"std::__terminate(void (*)())","symbolLocation":6,"imageIndex":51},{"imageOffset":69923,"symbol":"__cxa_rethrow","symbolLocation":99,"imageIndex":51},{"imageOffset":162293,"symbol":"objc_exception_rethrow","symbolLocation":37,"imageIndex":52},{"imageOffset":196416,"symbol":"-[NSApplication run]","symbolLocation":659,"imageIndex":53},{"imageOffset":254532,"imageIndex":15},{"imageOffset":1901734,"symbol":"QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":502,"imageIndex":4},{"imageOffset":1916707,"symbol":"QCoreApplication::exec()","symbolLocation":131,"imageIndex":4},{"imageOffset":28550,"symbol":"main","symbolLocation":310,"imageIndex":0},{"imageOffset":25624,"symbol":"start","symbolLocation":1896,"imageIndex":54}]},{"id":24277,"name":"QThread","frames":[{"imageOffset":33346,"symbol":"poll","symbolLocation":10,"imageIndex":48},{"imageOffset":2289037,"symbol":"qt_safe_poll(pollfd*, unsigned int, timespec const*)","symbolLocation":77,"imageIndex":4},{"imageOffset":2295295,"symbol":"QEventDispatcherUNIX::processEvents(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":815,"imageIndex":4},{"imageOffset":1901734,"symbol":"QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":502,"imageIndex":4},{"imageOffset":133307,"symbol":"QThread::exec()","symbolLocation":139,"imageIndex":4},{"imageOffset":137107,"imageIndex":4},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":49},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":49}]},{"id":24278,"frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":48},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":48},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":48},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":48},{"imageOffset":73040,"symbol":"XServerMachPort::ReceiveMessage(int&, void*, int&)","symbolLocation":94,"imageIndex":55},{"imageOffset":276677,"symbol":"MIDIProcess::MIDIInPortThread::Run()","symbolLocation":105,"imageIndex":55},{"imageOffset":179268,"symbol":"CADeprecated::XThread::RunHelper(void*)","symbolLocation":10,"imageIndex":55},{"imageOffset":183967,"symbol":"CADeprecated::CAPThread::Entry(CADeprecated::CAPThread*)","symbolLocation":77,"imageIndex":55},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":49},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":49}]},{"id":24283,"name":"caulk.messenger.shared:17","frames":[{"imageOffset":5326,"symbol":"semaphore_wait_trap","symbolLocation":10,"imageIndex":48},{"imageOffset":8318,"symbol":"caulk::semaphore::timed_wait(double)","symbolLocation":150,"imageIndex":56},{"imageOffset":8092,"symbol":"caulk::concurrent::details::worker_thread::run()","symbolLocation":30,"imageIndex":56},{"imageOffset":7344,"symbol":"void* caulk::thread_proxy<std::__1::tuple<caulk::thread::attributes, void (caulk::concurrent::details::worker_thread::*)(), std::__1::tuple<caulk::concurrent::details::worker_thread*>>>(void*)","symbolLocation":41,"imageIndex":56},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":49},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":49}]},{"id":24368,"name":"com.apple.NSEventThread","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":48},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":48},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":48},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":48},{"imageOffset":510272,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":145,"imageIndex":57},{"imageOffset":504779,"symbol":"__CFRunLoopRun","symbolLocation":1365,"imageIndex":57},{"imageOffset":501788,"symbol":"CFRunLoopRunSpecific","symbolLocation":560,"imageIndex":57},{"imageOffset":1698853,"symbol":"_NSEventThread","symbolLocation":132,"imageIndex":53},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":49},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":49}]},{"id":24700,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}]},{"id":24764,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}]},{"id":24765,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}]},{"id":24767,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":49}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4426285056,
    "CFBundleIdentifier" : "777.hairless-midiserial",
    "size" : 180224,
    "uuid" : "6eb2dae4-d68d-301b-b935-553e7de38838",
    "path" : "\/Users\/<USER>\/*\/hairless-midiserial.app\/Contents\/MacOS\/hairless-midiserial",
    "name" : "hairless-midiserial"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4432760832,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtWidgets",
    "size" : 4440064,
    "uuid" : "89354b8b-2ec2-337f-9989-f8e24e66ec31",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtWidgets.framework\/Versions\/5\/QtWidgets",
    "name" : "QtWidgets",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4445274112,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtGui",
    "size" : 5488640,
    "uuid" : "f6eaf280-a465-3cac-892d-73331684638c",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtGui.framework\/Versions\/5\/QtGui",
    "name" : "QtGui",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4438659072,
    "CFBundleShortVersionString" : "306.7.5",
    "CFBundleIdentifier" : "com.apple.Metal",
    "size" : 1966080,
    "uuid" : "6581cd53-8583-38d1-b4b9-773f1dc3f551",
    "path" : "\/System\/Library\/Frameworks\/Metal.framework\/Versions\/A\/Metal",
    "name" : "Metal",
    "CFBundleVersion" : "306.7.5"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4451905536,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtCore",
    "size" : 4751360,
    "uuid" : "a04c0588-4524-369a-8473-010d2cd62467",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtCore.framework\/Versions\/5\/QtCore",
    "name" : "QtCore",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4457484288,
    "size" : 294912,
    "uuid" : "d5cf0007-b92a-3d76-9549-1fb7bc428091",
    "path" : "\/System\/Library\/PrivateFrameworks\/GPUCompiler.framework\/Versions\/31001\/Libraries\/libllvm-flatbuffers.dylib",
    "name" : "libllvm-flatbuffers.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 6498684928,
    "size" : 16384,
    "uuid" : "d5cf0007-b778-37c5-917b-186e88376470",
    "path" : "\/System\/Library\/PrivateFrameworks\/GPUCompiler.framework\/Versions\/31001\/Libraries\/libGPUCompilerUtils.dylib",
    "name" : "libGPUCompilerUtils.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4427055104,
    "size" : 147456,
    "uuid" : "bcab46dd-fe50-3b76-a9e4-4ef840aa2bd2",
    "path" : "\/usr\/local\/Cellar\/libpng\/1.6.49\/lib\/libpng16.16.dylib",
    "name" : "libpng16.16.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4426932224,
    "size" : 49152,
    "uuid" : "a8976492-67b2-30f9-9009-b6edc2720565",
    "path" : "\/usr\/local\/Cellar\/md4c\/0.5.2\/lib\/libmd4c.0.5.2.dylib",
    "name" : "libmd4c.0.5.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4427845632,
    "size" : 540672,
    "uuid" : "1f031d9d-8dcc-3569-bddd-76324638905e",
    "path" : "\/usr\/local\/Cellar\/pcre2\/10.45\/lib\/libpcre2-16.0.dylib",
    "name" : "libpcre2-16.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4429217792,
    "size" : 688128,
    "uuid" : "c3d59a35-9312-3324-b1d5-2d05656b0740",
    "path" : "\/usr\/local\/Cellar\/zstd\/1.5.7\/lib\/libzstd.1.5.7.dylib",
    "name" : "libzstd.1.5.7.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4427251712,
    "size" : 16384,
    "uuid" : "db3732fe-b897-3594-97f8-8f973dba11b6",
    "path" : "\/usr\/local\/Cellar\/glib\/2.84.3\/lib\/libgthread-2.0.0.dylib",
    "name" : "libgthread-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4431196160,
    "size" : 999424,
    "uuid" : "0eb569ed-28cf-3524-90d6-5094614a570c",
    "path" : "\/usr\/local\/Cellar\/glib\/2.84.3\/lib\/libglib-2.0.0.dylib",
    "name" : "libglib-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4427530240,
    "size" : 180224,
    "uuid" : "63ab731a-7beb-3204-a38b-b360a4101509",
    "path" : "\/usr\/local\/Cellar\/gettext\/0.25\/lib\/libintl.8.dylib",
    "name" : "libintl.8.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4430004224,
    "size" : 573440,
    "uuid" : "16d2ea89-5eb5-34f5-9e6a-c13634bb0686",
    "path" : "\/usr\/local\/Cellar\/pcre2\/10.45\/lib\/libpcre2-8.0.dylib",
    "name" : "libpcre2-8.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8570318848,
    "size" : 720896,
    "uuid" : "5e4bec31-5b7d-3d92-82e9-d06fed2c7b83",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/platforms\/libqcocoa.dylib",
    "name" : "libqcocoa.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4444000256,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtDBus",
    "size" : 393216,
    "uuid" : "b221a0bd-8567-30bb-b7c7-a9a8363d2a7a",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtDBus.framework\/Versions\/5\/QtDBus",
    "name" : "QtDBus",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8571342848,
    "size" : 524288,
    "uuid" : "f3485bec-6ae1-300e-bab1-fa3187b0c01e",
    "path" : "\/usr\/local\/Cellar\/freetype\/2.13.3\/lib\/libfreetype.6.dylib",
    "name" : "libfreetype.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4432392192,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtPrintSupport",
    "size" : 180224,
    "uuid" : "e71b8452-02a7-3c4a-8495-40d025e480bb",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtPrintSupport.framework\/Versions\/5\/QtPrintSupport",
    "name" : "QtPrintSupport",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619327488,
    "size" : 147456,
    "uuid" : "f474f057-6aaf-3787-bf0d-227b68a12b4b",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/styles\/libqmacstyle.dylib",
    "name" : "libqmacstyle.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4445081600,
    "size" : 32768,
    "uuid" : "1f91cd9e-6d02-3341-8bd8-9b5779eb5c3b",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqgif.dylib",
    "name" : "libqgif.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4445200384,
    "size" : 32768,
    "uuid" : "f303c9fe-ab3d-31ce-a97e-7b6ddcf7a7df",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqicns.dylib",
    "name" : "libqicns.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4445138944,
    "size" : 32768,
    "uuid" : "eca4e00c-4a55-3383-b6d7-eebe3f62a59f",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqico.dylib",
    "name" : "libqico.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619610112,
    "size" : 32768,
    "uuid" : "6bd02a21-d32d-3779-a240-083fa0adfd99",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqjpeg.dylib",
    "name" : "libqjpeg.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8620371968,
    "size" : 573440,
    "uuid" : "f4be5d71-e90a-3ca5-b72f-5ba6f64da288",
    "path" : "\/usr\/local\/Cellar\/jpeg-turbo\/3.1.1\/lib\/libjpeg.8.3.2.dylib",
    "name" : "libjpeg.8.3.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619675648,
    "size" : 32768,
    "uuid" : "271ac1ef-0820-33ad-b461-a2c009d3295b",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqmacheif.dylib",
    "name" : "libqmacheif.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619737088,
    "size" : 32768,
    "uuid" : "8d995200-407e-3f45-858d-16830ea90978",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqmacjp2.dylib",
    "name" : "libqmacjp2.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619544576,
    "size" : 32768,
    "uuid" : "0dd18838-486e-3866-be7d-783ccaec8a15",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqpdf.dylib",
    "name" : "libqpdf.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8626561024,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtPdf",
    "size" : 5308416,
    "uuid" : "1f6e5c3f-e220-3a15-a432-7f01fd566986",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtPdf.framework\/Versions\/5\/QtPdf",
    "name" : "QtPdf",
    "CFBundleVersion" : "5.15.18"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8622465024,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtNetwork",
    "size" : 1081344,
    "uuid" : "7ff81240-**************-47ebb7cd8b62",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtNetwork.framework\/Versions\/5\/QtNetwork",
    "name" : "QtNetwork",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619859968,
    "size" : 32768,
    "uuid" : "67a0157a-a0e1-331e-8797-46b1e5aac642",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqsvg.dylib",
    "name" : "libqsvg.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8621076480,
    "CFBundleShortVersionString" : "5.15",
    "CFBundleIdentifier" : "org.qt-project.QtSvg",
    "size" : 229376,
    "uuid" : "bbe7a2b9-b177-3b40-9814-8789ffb64556",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/lib\/QtSvg.framework\/Versions\/5\/QtSvg",
    "name" : "QtSvg",
    "CFBundleVersion" : "5.15.16"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619798528,
    "size" : 32768,
    "uuid" : "1081f6a5-0d4f-38cc-9929-a2241b810fad",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqtga.dylib",
    "name" : "libqtga.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619999232,
    "size" : 49152,
    "uuid" : "d5d50065-619d-3bd1-9b73-c6b7f56314f5",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqtiff.dylib",
    "name" : "libqtiff.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8623853568,
    "size" : 442368,
    "uuid" : "0d8cd49b-491f-31ac-b8e3-2ad90db1f6e5",
    "path" : "\/usr\/local\/Cellar\/libtiff\/4.7.0\/lib\/libtiff.6.dylib",
    "name" : "libtiff.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8621424640,
    "size" : 131072,
    "uuid" : "79cca5fb-e119-3c0d-92bc-d607699fcdd3",
    "path" : "\/usr\/local\/Cellar\/xz\/5.8.1\/lib\/liblzma.5.dylib",
    "name" : "liblzma.5.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8619921408,
    "size" : 32768,
    "uuid" : "1446f187-0685-36e9-b099-df0564b08b27",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqwbmp.dylib",
    "name" : "libqwbmp.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8620138496,
    "size" : 32768,
    "uuid" : "780b4417-bdf7-31cf-87c0-ae9d10367576",
    "path" : "\/usr\/local\/Cellar\/qt@5\/5.15.16_2\/plugins\/imageformats\/libqwebp.dylib",
    "name" : "libqwebp.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8620199936,
    "size" : 32768,
    "uuid" : "6a64dc7b-3963-3ec6-9d17-ece06ff0b2d8",
    "path" : "\/usr\/local\/Cellar\/webp\/1.5.0\/lib\/libwebpmux.3.1.1.dylib",
    "name" : "libwebpmux.3.1.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8620077056,
    "size" : 16384,
    "uuid" : "b23245cd-894f-3fde-96cb-a9362d1b83b8",
    "path" : "\/usr\/local\/Cellar\/webp\/1.5.0\/lib\/libwebpdemux.2.0.16.dylib",
    "name" : "libwebpdemux.2.0.16.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8624394240,
    "size" : 360448,
    "uuid" : "78804bac-4bf8-34fd-8a5d-6fb769c14779",
    "path" : "\/usr\/local\/Cellar\/webp\/1.5.0\/lib\/libwebp.7.1.10.dylib",
    "name" : "libwebp.7.1.10.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8620265472,
    "size" : 16384,
    "uuid" : "7b64a3c3-1381-3860-89ed-c41f7a7bbe8c",
    "path" : "\/usr\/local\/Cellar\/webp\/1.5.0\/lib\/libsharpyuv.0.1.1.dylib",
    "name" : "libsharpyuv.0.1.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8621887488,
    "size" : 65536,
    "uuid" : "3f9b23a1-7826-30b9-b791-27c612efa764",
    "path" : "\/usr\/lib\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8625356800,
    "CFBundleShortVersionString" : "440.2",
    "CFBundleIdentifier" : "com.apple.audio.AppleHDAHALPlugIn",
    "size" : 32768,
    "uuid" : "01ffcedc-b862-3154-9715-ff565fd4b625",
    "path" : "\/System\/Library\/Extensions\/AppleHDA.kext\/Contents\/PlugIns\/AppleHDAHALPlugIn.bundle\/Contents\/MacOS\/AppleHDAHALPlugIn",
    "name" : "AppleHDAHALPlugIn",
    "CFBundleVersion" : "440.2"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8626466816,
    "CFBundleShortVersionString" : "16.5.14",
    "CFBundleIdentifier" : "com.apple.driver.AppleIntelHD4000GraphicsMTLDriver",
    "size" : 8192,
    "uuid" : "4aca8344-1dbd-368b-a489-f3bff99ae117",
    "path" : "\/System\/Library\/Extensions\/AppleIntelHD4000GraphicsMTLDriver.bundle\/Contents\/MacOS\/AppleIntelHD4000GraphicsMTLDriver",
    "name" : "AppleIntelHD4000GraphicsMTLDriver",
    "CFBundleVersion" : "16.0.5"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8638754816,
    "size" : 417792,
    "uuid" : "d5ce0008-cae6-34e9-90e3-7f28f989a2cb",
    "path" : "\/System\/Library\/Extensions\/AppleIntelHD4000GraphicsMTLDriver.bundle\/Contents\/MacOS\/AppleIntelHD4000GraphicsMTLDriver_real",
    "name" : "AppleIntelHD4000GraphicsMTLDriver_real"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 10194587648,
    "CFBundleShortVersionString" : "16.0.12",
    "CFBundleIdentifier" : "com.apple.GeForceMTLDriver",
    "size" : 1835008,
    "uuid" : "dc9e7ee8-2b92-3fc3-9524-fcae13d2f69d",
    "path" : "\/System\/Library\/Extensions\/GeForceMTLDriver.bundle\/Contents\/MacOS\/GeForceMTLDriver",
    "name" : "GeForceMTLDriver",
    "CFBundleVersion" : "16.0.0"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 8637444096,
    "size" : 16384,
    "uuid" : "a8c8d7c3-9a85-391b-a3f3-430a2bf54b91",
    "path" : "\/System\/Library\/Extensions\/GeForceMTLDriver.bundle\/Contents\/MacOS\/Shim.dylib",
    "name" : "Shim.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703376035840,
    "size" : 237568,
    "uuid" : "69e3eb4e-fb58-3fde-a3f0-4f1a5ddcd2d1",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703376273408,
    "size" : 49152,
    "uuid" : "51a43b06-feb4-3836-9e4f-21b69bb13726",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703374884864,
    "size" : 557048,
    "uuid" : "77738df4-82b8-3690-937e-cc1d4981a630",
    "path" : "\/usr\/lib\/system\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703375949824,
    "size" : 86004,
    "uuid" : "717bce6c-23b4-31dc-9d8b-6859639a38d5",
    "path" : "\/usr\/lib\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703372513280,
    "size" : 241601,
    "uuid" : "dd8ed00f-eb16-3edc-8a53-1916f5e82556",
    "path" : "\/usr\/lib\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703427260416,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.AppKit",
    "size" : 16818168,
    "uuid" : "5ad23f03-65ba-3aa0-a14e-14b45615ded3",
    "path" : "\/System\/Library\/Frameworks\/AppKit.framework\/Versions\/C\/AppKit",
    "name" : "AppKit",
    "CFBundleVersion" : "2299.77.138"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703372754944,
    "size" : 624176,
    "uuid" : "5baa56eb-4369-3aeb-9aa7-f973e58d9b8e",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703805300736,
    "CFBundleShortVersionString" : "2.0",
    "CFBundleIdentifier" : "com.apple.audio.midi.CoreMIDI",
    "size" : 450558,
    "uuid" : "7320de0b-4b1b-35a6-8190-1946262bec3d",
    "path" : "\/System\/Library\/Frameworks\/CoreMIDI.framework\/Versions\/A\/CoreMIDI",
    "name" : "CoreMIDI",
    "CFBundleVersion" : "88"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703541334016,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.audio.caulk",
    "size" : 151540,
    "uuid" : "4d56b5eb-40ec-3980-8272-018c6373a3c1",
    "path" : "\/System\/Library\/PrivateFrameworks\/caulk.framework\/Versions\/A\/caulk",
    "name" : "caulk"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703376687104,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 4829184,
    "uuid" : "c21f327f-e547-3cf0-9a91-721ab86fe6e6",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/Versions\/A\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "1979.104.902"
  }
],
  "sharedCache" : {
  "base" : 140703372136448,
  "size" : 21474836480,
  "uuid" : "4f517cca-c056-37a5-b5f9-507b033eaf3d"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=481.1M resident=0K(0%) swapped_out_or_unallocated=481.1M(100%)\nWritable regions: Total=5.9G written=0K(0%) resident=0K(0%) swapped_out=0K(0%) unallocated=5.9G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nAccelerate framework               128K        1 \nActivity Tracing                   256K        1 \nCG backing stores                 2160K        4 \nCG image                            64K        5 \nColorSync                          264K       29 \nCoreAnimation                       68K        7 \nCoreGraphics                        20K        3 \nCoreUI image data                 1936K       13 \nFoundation                          16K        1 \nKernel Alloc Once                    8K        1 \nMALLOC                           226.8M       64 \nMALLOC guard page                   32K        7 \nMALLOC_NANO (reserved)           384.0M        1         reserved VM address space (unallocated)\nSTACK GUARD                       56.0M        9 \nStack                             12.1M        9 \nVM_ALLOCATE                       97.7M       15 \nVM_ALLOCATE (reserved)             5.2G        7         reserved VM address space (unallocated)\n__CTF                               824        1 \n__DATA                            20.2M      387 \n__DATA_CONST                      16.0M      228 \n__DATA_DIRTY                       736K      114 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       178.7M       50 \n__OBJC_RO                         66.3M        1 \n__OBJC_RW                         2011K        2 \n__TEXT                           302.4M      407 \ndsce.bs.names                      112K        1 \ndyld private memory                260K        2 \nmapped file                       79.7M       27 \nshared memory                      840K       26 \n===========                     =======  ======= \nTOTAL                              6.6G     1424 \nTOTAL, minus reserved VM space     1.0G     1424 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "82b2cc77384b64d4c6b396e88c40ea2ae4b0f53a",
  "trialInfo" : {
  "rollouts" : [

  ],
  "experiments" : [

  ]
}
}

Model: MacBookPro10,1, BootROM 429.0.0.0.0, 4 processors, Quad-Core Intel Core i7, 2,3 GHz, 16 GB, SMC 2.3f36
Graphics: Intel HD Graphics 4000, Intel HD Graphics 4000, Built-In
Display: Color LCD, 2880 x 1800 Retina, Main, MirrorOff, Online
Graphics: NVIDIA GeForce GT 650M, NVIDIA GeForce GT 650M, PCIe, 1 GB
Memory Module: BANK 0/DIMM0, 8 GB, DDR3, 1600 MHz, 0x80AD, 0x484D5434314753364D465238432D50422020
Memory Module: BANK 1/DIMM0, 8 GB, DDR3, 1600 MHz, 0x80AD, 0x484D5434314753364D465238432D50422020
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0xEF), Broadcom BCM43xx 1.0 (7.77.111.1 AirPortDriverBrcmNIC-1772.1)
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
Serial ATA Device: TS240GJDM725, 240,06 GB
USB Device: USB30Bus
USB Device: USB3.0 Hub
USB Device: USB2.0 Hub
USB Device: ROCCAT Kain 100
USB Device: USB Serial
USB Device: Yealink UH37
USB Device: USB20Bus
USB Device: hub_device
USB Device: FaceTime HD Camera (Built-in)
USB Device: USB20Bus
USB Device: hub_device
USB Device: hub_device
USB Device: Apple Internal Keyboard / Trackpad
USB Device: BRCM20702 Hub
USB Device: Bluetooth USB Host Controller
USB Device: composite_device
USB Device: composite_device
Thunderbolt Bus: MacBook Pro, Apple Inc., 23.4
