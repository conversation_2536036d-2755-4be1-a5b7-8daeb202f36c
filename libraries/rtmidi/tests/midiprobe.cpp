// midiprobe.cpp
//
// Simple program to check MIDI inputs and outputs.
//
// by <PERSON>, 2003-2012.

#include <iostream>
#include <cstdlib>
#include <map>
#include "RtMidi.h"

int main()
{
  // Create an api map.
  std::map<int, std::string> apiMap;
  apiMap[RtMidi::MACOSX_CORE] = "OS-X CoreMidi";
  apiMap[RtMidi::WINDOWS_MM] = "Windows MultiMedia";
  apiMap[RtMidi::UNIX_JACK] = "Jack Client";
  apiMap[RtMidi::LINUX_ALSA] = "Linux ALSA";
  apiMap[RtMidi::RTMIDI_DUMMY] = "RtMidi Dummy";

  std::vector< RtMidi::Api > apis;
  RtMidi :: getCompiledApi( apis );

  std::cout << "\nCompiled APIs:\n";
  for ( unsigned int i=0; i<apis.size(); i++ )
    std::cout << "  " << apiMap[ apis[i] ] << std::endl;

  RtMidiIn  *midiin = 0;
  RtMidiOut *midiout = 0;

  try {

    // RtMidiIn constructor ... exception possible
    midiin = new RtMidiIn();

    std::cout << "\nCurrent input API: " << apiMap[ midiin->getCurrentApi() ] << std::endl;

    // Check inputs.
    unsigned int nPorts = midiin->getPortCount();
    std::cout << "\nThere are " << nPorts << " MIDI input sources available.\n";

    for ( unsigned i=0; i<nPorts; i++ ) {
      std::string portName = midiin->getPortName(i);
      std::cout << "  Input Port #" << i+1 << ": " << portName << '\n';
    }

    // RtMidiOut constructor ... exception possible
    midiout = new RtMidiOut();

    std::cout << "\nCurrent output API: " << apiMap[ midiout->getCurrentApi() ] << std::endl;

    // Check outputs.
    nPorts = midiout->getPortCount();
    std::cout << "\nThere are " << nPorts << " MIDI output ports available.\n";

    for ( unsigned i=0; i<nPorts; i++ ) {
      std::string portName = midiout->getPortName(i);
      std::cout << "  Output Port #" << i+1 << ": " << portName << std::endl;
    }
    std::cout << std::endl;

  } catch ( RtMidiError &error ) {
    error.printMessage();
  }

  delete midiin;
  delete midiout;

  return 0;
}
