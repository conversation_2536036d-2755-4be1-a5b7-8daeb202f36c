### Do not edit -- Generated by 'configure --with-whatever' from <PERSON><PERSON><PERSON>.in
### Rt<PERSON>id<PERSON> tests Makefile - for various flavors of unix

PROGRAMS = midiprobe midiout qmidiin cmidiin sysextest
RM = /bin/rm
SRC_PATH = ..
INCLUDE = @top_srcdir@
OBJECT_PATH = @object_path@
vpath %.o $(OBJECT_PATH)

OBJECTS	=	@top_srcdir@/RtMidi.o

CC       = @CXX@
DEFS     = @CPPFLAGS@
CFLAGS   = @CXXFLAGS@
CFLAGS  += -I$(INCLUDE) -I$(INCLUDE)/include
LIBRARY  = @LIBS@

%.o : $(SRC_PATH)/%.cpp
	$(CC) $(CFLAGS) $(DEFS) -c $(<) -o $(OBJECT_PATH)/$@

all : $(PROGRAMS)

midiprobe : @srcdir@/midiprobe.cpp $(OBJECTS)
	$(CC) $(CFLAGS) $(DEFS) -o midiprobe $^ $(LIBRARY)

midiout : @srcdir@/midiout.cpp $(OBJECTS)
	$(CC) $(CFLAGS) $(DEFS) -o midiout $^ $(LIBRARY)

qmidiin : @srcdir@/qmidiin.cpp $(OBJECTS)
	$(CC) $(CFLAGS) $(DEFS) -o qmidiin $^ $(LIBRARY)

cmidiin : @srcdir@/cmidiin.cpp $(OBJECTS)
	$(CC) $(CFLAGS) $(DEFS) -o cmidiin $^ $(LIBRARY)

sysextest : @srcdir@/sysextest.cpp $(OBJECTS)
	$(CC) $(CFLAGS) $(DEFS) -o sysextest $^ $(LIBRARY)

clean : 
	$(RM) -f $(OBJECT_PATH)/*.o
	$(RM) -f $(PROGRAMS) *.exe
	$(RM) -f *~
	$(RM) -fR *.dSYM

distclean: clean
	$(RM) -f Makefile

strip : 
	strip $(PROGRAMS)
