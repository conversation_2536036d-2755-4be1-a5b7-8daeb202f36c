a:link, a:visited {
    color: #00732F;
    text-decoration: none;
    font-weight: bold;
}

body {
    font: normal 400 14px/1.2 Arial;
    margin-top: 85px;
}

h1 {
    margin: 0;
}

h2 {
    font: 500 20px/1.2 Arial;
}

h3.fn, span.fn {
    -moz-border-radius: 7px 7px 7px 7px;
    -webkit-border-radius: 7px 7px 7px 7px;
    border-radius: 7px 7px 7px 7px;
    background-color: #F6F6F6;
    border-width: 1px;
    border-style: solid;
    border-color: #E6E6E6;
    word-spacing: 3px;
    padding: 3px 5px;
}

table, pre {
    -moz-border-radius: 7px 7px 7px 7px;
    -webkit-border-radius: 7px 7px 7px 7px;
    border-radius: 7px 7px 7px 7px;
    background-color: #F6F6F6;
    border: 1px solid #E6E6E6;
    border-collapse: separate;
    font-size: 12px;
    line-height: 1.2;
    margin-bottom: 25px;
    margin-left: 15px;
}

table td {
    padding: 3px 15px 3px 20px;
}

table tr.even {
    background-color: white;
    color: #66666E;
}

table tr.odd {
    background-color: #F6F6F6;
    color: #66666E;
}

li {
    margin-bottom: 10px;
    padding-left: 12px;
}

.cpp {
    display: block;
    margin: 10;
    overflow: hidden;
    overflow-x: hidden;
    overflow-y: hidden;
    padding: 20px 0 20px 0;
}

.footer {
    margin-top: 50px;
}

.memItemLeft {
    padding-right: 3px;
}

.memItemRight {
    padding: 3px 15px 3px 0;
}

.qml {
    display: block;
    margin: 10;
    overflow: hidden;
    overflow-x: hidden;
    overflow-y: hidden;
    padding: 20px 0 20px 0;
}

.qmldefault {
    padding-left: 5px;
    float: right;
    color: red;
}

.qmlreadonly {
    padding-left: 5px;
    float: right;
    color: #254117;
}

.rightAlign {
    padding: 3px 5px 3px 10px;
    text-align: right;
}

.title {
    background-color: white;
    color: #44A51C;
    font-family: Verdana;
    font-size: 35px;
    font-weight: normal;
    left: 0;
    padding-bottom: 5px;
    padding-left: 16px;
    padding-top: 20px;
    position: absolute;
    right: 0;
    top: 0;
}

.toc {
    float: right;
    -moz-border-radius: 7px 7px 7px 7px;
    -webkit-border-radius: 7px 7px 7px 7px;
    border-radius: 7px 7px 7px 7px;
    background-color: #F6F6F6;
    border: 1px solid #DDD;
    margin: 0 20px 10px 10px;
    padding: 20px 15px 20px 20px;
    height: auto;
    width: 200px;
}
