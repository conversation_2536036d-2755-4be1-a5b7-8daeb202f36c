<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>604</width>
    <height>485</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout" stretch="3,1">
     <item>
      <widget class="QPlainTextEdit" name="recvEdit">
       <property name="maximumBlockCount">
        <number>800</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPlainTextEdit" name="sendEdit"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <layout class="QFormLayout" name="formLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Port:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QComboBox" name="portBox"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>BaudRate:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QComboBox" name="baudRateBox"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>DataBits:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="dataBitsBox"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>Parity:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QComboBox" name="parityBox"/>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>StopBits:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QComboBox" name="stopBitsBox"/>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>QueryMode:</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QComboBox" name="queryModeBox"/>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>Timeout:</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QSpinBox" name="timeoutBox">
         <property name="suffix">
          <string> ms</string>
         </property>
         <property name="minimum">
          <number>-1</number>
         </property>
         <property name="maximum">
          <number>10000</number>
         </property>
         <property name="singleStep">
          <number>10</number>
         </property>
         <property name="value">
          <number>10</number>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="HLed" name="led" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>20</width>
           <height>20</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>25</width>
           <height>25</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="openCloseButton">
         <property name="text">
          <string>Open/Close</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="sendButton">
       <property name="text">
        <string>Send</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>HLed</class>
   <extends>QWidget</extends>
   <header>hled.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
