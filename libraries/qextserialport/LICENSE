From QextSerialPort 1.2-beta on, we use MIT license for QextSerialPort project.

== License ==

    Copyright (c) 2000-2003 <PERSON>
    Copyright (c) 2004-2007 <PERSON>
    Copyright (c) 2007 Michal <PERSON>t
    Copyright (c) 2008 <PERSON>
    Copyright (c) 2009-2010 <PERSON>
    Copyright (c) 2011 Debao Zhang
    
    Web: http://code.google.com/p/qextserialport/
    
    Permission is hereby granted, free of charge, to any person obtaining
    a copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to
    permit persons to whom the Software is furnished to do so, subject to
    the following conditions:
    
    The above copyright notice and this permission notice shall be
    included in all copies or substantial portions of the Software.
    
    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
    LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
    OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
    WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

== Why license needed? ==

 Many users complains that, without a proper licence they can not use this library.

 * http://groups.google.com/group/qextserialport/browse_thread/thread/0e8756920b01da82

    Hi, 
    we are considering using a modified version of QExtSerialPort in one of our 
    projects (Qt Creator, http://qt.gitorious.org/qt-creator). 
    Would it be possible to add license header information or a license file to the   
    QExtSerialPort code base? - This would make re-use of the code base easier. 
    If that is not  possible, could we redistribute the source code with BSD- 
    license headers manually added? 

And

    I am also considering packaging the software for Debian, but I 
    couldn't do it yet just because of the license. 

 * http://code.google.com/p/qextserialport/issues/detail?id=8

    Questions:
    Can I use qextserialport in a commercial product?
    If yes, how?
    Compile it in? I guess no.
    If I can use it as a library, how should the README be formulated?
    Is the "MIT license" from 2008 appropriate?

== Why can we use MIT? ==

Form the history of [http://lists.trolltech.com/qt-interest/2004-12/msg01022.html qt-interest mail list]

 * Wayne Roth, the original author of the project, had said that:

    the code is in the public domain. Do whatever you like with it. Right 
    now I have too many other things to do to put any serious time into
    fixing it.  Trolltech should be aware of this already; they asked 
    about a license when they offered to host the tarball.

 * Stefan Sander, the maintainer of qextserialport on sourceforge, said that

    Hello,
    My project registration at !SourceForge have been approved.
    http://www.sf.net/projects/qextserialport
    I thought an initial licence of Public Domain would be best solution.
    Someone wrote: - Because its public domain, some could fork it under different licenses -

And from [http://groups.google.com/group/qextserialport/browse_thread/thread/fbcddbfb4a0b5a51?pli=1 this thread] on qesp mail list, we can see that, current maintainers and users agree with a MIT licence.

 * Brandon Fosdick,

    I would vote for BSD or MIT :) 

 * Liam Staskawicz,

    That works for me - let's call it MIT and go for it :) 

And from [[https://groups.google.com/forum/?fromgroups#!topic/qextserialport/P_5TrNHBICE this other thread]] on the same mailing list:

 * Michal Policht,

    I agree to license.
