/********************************************************************************
** Form generated from reading UI file 'settingsdialog.ui'
**
** Created by: Qt User Interface Compiler version 5.15.16
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SETTINGSDIALOG_H
#define UI_SETTINGSDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SettingsDialog
{
public:
    QDialogButtonBox *buttonBox;
    QGroupBox *groupBox;
    QWidget *formLayoutWidget;
    QFormLayout *formLayout;
    QLabel *baudRateLabel;
    QComboBox *cmb_baud;
    QLabel *dataBitsLabel;
    QComboBox *cmb_dataBits;
    QLabel *parityLabel;
    QComboBox *cmb_parity;
    QLabel *stopBitSLabel;
    QComboBox *cmb_stop;
    QLabel *flowControlLabel;
    QComboBox *cmb_flow;
    QPushButton *btn_defaults;
    QGroupBox *groupBox_2;
    QWidget *formLayoutWidget_2;
    QFormLayout *formLayout_2;
    QLabel *linesOfDebugScrollbackLabel;
    QSpinBox *spn_scrollback;

    void setupUi(QDialog *SettingsDialog)
    {
        if (SettingsDialog->objectName().isEmpty())
            SettingsDialog->setObjectName(QString::fromUtf8("SettingsDialog"));
        SettingsDialog->resize(379, 403);
        buttonBox = new QDialogButtonBox(SettingsDialog);
        buttonBox->setObjectName(QString::fromUtf8("buttonBox"));
        buttonBox->setGeometry(QRect(20, 360, 341, 32));
        buttonBox->setOrientation(Qt::Horizontal);
        buttonBox->setStandardButtons(QDialogButtonBox::Cancel|QDialogButtonBox::Ok);
        groupBox = new QGroupBox(SettingsDialog);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        groupBox->setGeometry(QRect(20, 10, 331, 241));
        formLayoutWidget = new QWidget(groupBox);
        formLayoutWidget->setObjectName(QString::fromUtf8("formLayoutWidget"));
        formLayoutWidget->setGeometry(QRect(10, 20, 311, 181));
        formLayout = new QFormLayout(formLayoutWidget);
        formLayout->setObjectName(QString::fromUtf8("formLayout"));
        formLayout->setFieldGrowthPolicy(QFormLayout::AllNonFixedFieldsGrow);
        formLayout->setContentsMargins(0, 0, 0, 0);
        baudRateLabel = new QLabel(formLayoutWidget);
        baudRateLabel->setObjectName(QString::fromUtf8("baudRateLabel"));

        formLayout->setWidget(0, QFormLayout::LabelRole, baudRateLabel);

        cmb_baud = new QComboBox(formLayoutWidget);
        cmb_baud->setObjectName(QString::fromUtf8("cmb_baud"));

        formLayout->setWidget(0, QFormLayout::FieldRole, cmb_baud);

        dataBitsLabel = new QLabel(formLayoutWidget);
        dataBitsLabel->setObjectName(QString::fromUtf8("dataBitsLabel"));

        formLayout->setWidget(1, QFormLayout::LabelRole, dataBitsLabel);

        cmb_dataBits = new QComboBox(formLayoutWidget);
        cmb_dataBits->setObjectName(QString::fromUtf8("cmb_dataBits"));

        formLayout->setWidget(1, QFormLayout::FieldRole, cmb_dataBits);

        parityLabel = new QLabel(formLayoutWidget);
        parityLabel->setObjectName(QString::fromUtf8("parityLabel"));

        formLayout->setWidget(2, QFormLayout::LabelRole, parityLabel);

        cmb_parity = new QComboBox(formLayoutWidget);
        cmb_parity->setObjectName(QString::fromUtf8("cmb_parity"));

        formLayout->setWidget(2, QFormLayout::FieldRole, cmb_parity);

        stopBitSLabel = new QLabel(formLayoutWidget);
        stopBitSLabel->setObjectName(QString::fromUtf8("stopBitSLabel"));

        formLayout->setWidget(3, QFormLayout::LabelRole, stopBitSLabel);

        cmb_stop = new QComboBox(formLayoutWidget);
        cmb_stop->setObjectName(QString::fromUtf8("cmb_stop"));

        formLayout->setWidget(3, QFormLayout::FieldRole, cmb_stop);

        flowControlLabel = new QLabel(formLayoutWidget);
        flowControlLabel->setObjectName(QString::fromUtf8("flowControlLabel"));

        formLayout->setWidget(4, QFormLayout::LabelRole, flowControlLabel);

        cmb_flow = new QComboBox(formLayoutWidget);
        cmb_flow->setObjectName(QString::fromUtf8("cmb_flow"));

        formLayout->setWidget(4, QFormLayout::FieldRole, cmb_flow);

        btn_defaults = new QPushButton(groupBox);
        btn_defaults->setObjectName(QString::fromUtf8("btn_defaults"));
        btn_defaults->setGeometry(QRect(172, 210, 141, 28));
        groupBox_2 = new QGroupBox(SettingsDialog);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setGeometry(QRect(20, 270, 331, 71));
        formLayoutWidget_2 = new QWidget(groupBox_2);
        formLayoutWidget_2->setObjectName(QString::fromUtf8("formLayoutWidget_2"));
        formLayoutWidget_2->setGeometry(QRect(0, 20, 321, 31));
        formLayout_2 = new QFormLayout(formLayoutWidget_2);
        formLayout_2->setObjectName(QString::fromUtf8("formLayout_2"));
        formLayout_2->setFieldGrowthPolicy(QFormLayout::AllNonFixedFieldsGrow);
        formLayout_2->setContentsMargins(0, 0, 0, 0);
        linesOfDebugScrollbackLabel = new QLabel(formLayoutWidget_2);
        linesOfDebugScrollbackLabel->setObjectName(QString::fromUtf8("linesOfDebugScrollbackLabel"));

        formLayout_2->setWidget(0, QFormLayout::LabelRole, linesOfDebugScrollbackLabel);

        spn_scrollback = new QSpinBox(formLayoutWidget_2);
        spn_scrollback->setObjectName(QString::fromUtf8("spn_scrollback"));
        spn_scrollback->setMinimum(2);
        spn_scrollback->setMaximum(2000);
        spn_scrollback->setSingleStep(10);
        spn_scrollback->setValue(50);

        formLayout_2->setWidget(0, QFormLayout::FieldRole, spn_scrollback);

        QWidget::setTabOrder(cmb_baud, cmb_dataBits);
        QWidget::setTabOrder(cmb_dataBits, cmb_parity);
        QWidget::setTabOrder(cmb_parity, cmb_stop);
        QWidget::setTabOrder(cmb_stop, cmb_flow);
        QWidget::setTabOrder(cmb_flow, btn_defaults);
        QWidget::setTabOrder(btn_defaults, spn_scrollback);
        QWidget::setTabOrder(spn_scrollback, buttonBox);

        retranslateUi(SettingsDialog);
        QObject::connect(buttonBox, SIGNAL(accepted()), SettingsDialog, SLOT(accept()));
        QObject::connect(buttonBox, SIGNAL(rejected()), SettingsDialog, SLOT(reject()));
        QObject::connect(buttonBox, SIGNAL(accepted()), SettingsDialog, SLOT(accept()));

        QMetaObject::connectSlotsByName(SettingsDialog);
    } // setupUi

    void retranslateUi(QDialog *SettingsDialog)
    {
        SettingsDialog->setWindowTitle(QCoreApplication::translate("SettingsDialog", "Settings", nullptr));
        groupBox->setTitle(QCoreApplication::translate("SettingsDialog", "Serial Port Settings", nullptr));
        baudRateLabel->setText(QCoreApplication::translate("SettingsDialog", "Baud rate", nullptr));
        dataBitsLabel->setText(QCoreApplication::translate("SettingsDialog", "Data Bits", nullptr));
        parityLabel->setText(QCoreApplication::translate("SettingsDialog", "Parity", nullptr));
        stopBitSLabel->setText(QCoreApplication::translate("SettingsDialog", "Stop Bit(s)", nullptr));
        flowControlLabel->setText(QCoreApplication::translate("SettingsDialog", "Flow Control", nullptr));
        btn_defaults->setText(QCoreApplication::translate("SettingsDialog", "Restore Defaults", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("SettingsDialog", "Debugging Output", nullptr));
        linesOfDebugScrollbackLabel->setText(QCoreApplication::translate("SettingsDialog", "Lines of Debug Scrollback", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SettingsDialog: public Ui_SettingsDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SETTINGSDIALOG_H
