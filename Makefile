#############################################################################
# Makefile for building: hairless-midiserial.app/Contents/MacOS/hairless-midiserial
# Generated by qmake (3.1) (Qt 5.15.16)
# Project:  hairless-midiserial.pro
# Template: app
# Command: /usr/local/opt/qt@5/bin/qmake -o Makefile hairless-midiserial.pro CONFIG+=sdk_no_version_check
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
CXX           = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
DEFINES       = -DAPPNAME="hairless-midiserial" -DVERSION=\"0.4\" -D__MACOSX_CORE__ -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=10.13 -Wall -Wextra -fPIC $(DEFINES)
CXXFLAGS      = -pipe -stdlib=libc++ -O2 $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=10.13 -Wall -Wextra -fPIC $(DEFINES)
INCPATH       = -I. -Isrc -Ilibraries/qextserialport/src -Ilibraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I. -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/OpenGL.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/AGL.framework/Headers/ -I. -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -F/usr/local/Cellar/qt@5/5.15.16_2/lib
QMAKE         = /usr/local/opt/qt@5/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /usr/local/opt/qt@5/bin/qmake -install qinstall
QINSTALL_PROGRAM = /usr/local/opt/qt@5/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = hairless-midiserial1.0.0
DISTDIR = /Users/<USER>/dev/hairless-midiserial-Qt5/.tmp/hairless-midiserial1.0.0
LINK          = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
LFLAGS        = -stdlib=libc++ -headerpad_max_install_names $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=10.13 -Wl,-rpath,@executable_path/../Frameworks
LIBS          = $(SUBLIBS) -F/usr/local/Cellar/qt@5/5.15.16_2/lib -framework CoreMidi -framework CoreAudio -framework CoreFoundation -framework QtWidgets -framework QtGui -framework AppKit -framework Metal -framework QtCore -framework DiskArbitration -framework IOKit -framework OpenGL -framework AGL   
AR            = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar cq
RANLIB        = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib -s
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = src/main.cpp \
		src/Bridge.cpp \
		src/BlinkenLight.cpp \
		src/ui/mainwindow.cpp \
		src/ui/aboutdialog.cpp \
		src/ui/settingsdialog.cpp \
		libraries/qextserialport/src/qextserialport.cpp \
		libraries/qextserialport/src/qextserialenumerator.cpp \
		libraries/qextserialport/src/qextserialport_unix.cpp \
		libraries/qextserialport/src/qextserialenumerator_osx.cpp \
		libraries/rtmidi/RtMidi.cpp \
		src/QRtMidiIn.cpp \
		src/PortLatency.cpp \
		src/PortLatency_osx.cpp qrc_resources.cpp \
		moc_Bridge.cpp \
		moc_BlinkenLight.cpp \
		moc_mainwindow.cpp \
		moc_settingsdialog.cpp \
		moc_aboutdialog.cpp \
		moc_QRtMidiIn.cpp \
		moc_PortLatency.cpp
OBJECTS       = main.o \
		Bridge.o \
		BlinkenLight.o \
		mainwindow.o \
		aboutdialog.o \
		settingsdialog.o \
		qextserialport.o \
		qextserialenumerator.o \
		qextserialport_unix.o \
		qextserialenumerator_osx.o \
		RtMidi.o \
		QRtMidiIn.o \
		PortLatency.o \
		PortLatency_osx.o \
		qrc_resources.o \
		moc_Bridge.o \
		moc_BlinkenLight.o \
		moc_mainwindow.o \
		moc_settingsdialog.o \
		moc_aboutdialog.o \
		moc_QRtMidiIn.o \
		moc_PortLatency.o
DIST          = /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qdevice.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/device_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/unix.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/macx.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/sanitize.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qconfig.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bodymovin_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_clipboard_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_graphics_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exclusive_builds.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resolve_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/objective_c.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/mac.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/warn_on.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/moc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/opengl.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/uic.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/thread.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qmake_use.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/file_copies.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/rez.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/asset_catalogs.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/testcase_targets.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exceptions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/yacc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/lex.prf \
		hairless-midiserial.pro src/Bridge.h \
		src/Settings.h \
		src/BlinkenLight.h \
		src/ui/mainwindow.h \
		src/ui/settingsdialog.h \
		src/ui/aboutdialog.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialenumerator.h \
		libraries/qextserialport/src/qextserialport_global.h \
		libraries/rtmidi/RtMidi.h \
		src/QRtMidiIn.h \
		src/PortLatency.h src/main.cpp \
		src/Bridge.cpp \
		src/BlinkenLight.cpp \
		src/ui/mainwindow.cpp \
		src/ui/aboutdialog.cpp \
		src/ui/settingsdialog.cpp \
		libraries/qextserialport/src/qextserialport.cpp \
		libraries/qextserialport/src/qextserialenumerator.cpp \
		libraries/qextserialport/src/qextserialport_unix.cpp \
		libraries/qextserialport/src/qextserialenumerator_osx.cpp \
		libraries/rtmidi/RtMidi.cpp \
		src/QRtMidiIn.cpp \
		src/PortLatency.cpp \
		src/PortLatency_osx.cpp
QMAKE_TARGET  = hairless-midiserial
DESTDIR       = 
TARGET        = hairless-midiserial.app/Contents/MacOS/hairless-midiserial

####### Custom Variables
EXPORT_QMAKE_MAC_SDK = macosx
EXPORT_QMAKE_MAC_SDK_VERSION = 15.4
EXPORT_QMAKE_XCODE_DEVELOPER_PATH = /Applications/Xcode.app/Contents/Developer
EXPORT__QMAKE_STASH_ = /Users/<USER>/dev/hairless-midiserial-Qt5/.qmake.stash
EXPORT_VALID_ARCHS = x86_64
EXPORT_DEFAULT_ARCHS = x86_64
EXPORT_ARCHS = $(filter $(EXPORT_VALID_ARCHS), $(if $(ARCHS), $(ARCHS), $(if $(EXPORT_DEFAULT_ARCHS), $(EXPORT_DEFAULT_ARCHS), $(EXPORT_VALID_ARCHS))))
EXPORT_ARCH_ARGS = $(foreach arch, $(if $(EXPORT_ARCHS), $(EXPORT_ARCHS), $(EXPORT_VALID_ARCHS)), -arch $(arch))
EXPORT__PRO_FILE_ = /Users/<USER>/dev/hairless-midiserial-Qt5/hairless-midiserial.pro


include /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.mk
first: all
####### Build rules

hairless-midiserial.app/Contents/MacOS/hairless-midiserial: ui_mainwindow.h ui_settingsdialog.h ui_aboutdialog.h $(OBJECTS)  
	@test -d hairless-midiserial.app/Contents/MacOS/ || mkdir -p hairless-midiserial.app/Contents/MacOS/
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: hairless-midiserial.pro /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qdevice.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/device_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/unix.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/macx.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/sanitize.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qconfig.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bodymovin_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_clipboard_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_graphics_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exclusive_builds.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resolve_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/objective_c.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/mac.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/warn_on.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/moc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/opengl.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/uic.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/thread.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qmake_use.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/file_copies.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/rez.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/asset_catalogs.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/testcase_targets.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exceptions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/yacc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/lex.prf \
		hairless-midiserial.pro \
		resources.qrc \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Resources/QtWidgets.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Resources/QtGui.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Resources/QtCore.prl
	$(QMAKE) -o Makefile hairless-midiserial.pro CONFIG+=sdk_no_version_check
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_pre.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qdevice.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/device_config.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/unix.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/mac.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/macx.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/sanitize.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base-mac.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang-mac.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qconfig.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bodymovin_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bootstrap_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_clipboard_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designercomponents_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_edid_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fb_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_graphics_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldebug_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldevtools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickparticles_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickshapes_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_service_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_theme_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uiplugin.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_functions.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_config.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_post.prf:
.qmake.stash:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exclusive_builds.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/toolchain.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/toolchain.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_pre.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_pre.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resolve_config.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_post.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_post.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/objective_c.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/mac.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/warn_on.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources_functions.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/moc.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/opengl.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/uic.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/thread.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qmake_use.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/file_copies.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/rez.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/asset_catalogs.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/testcase_targets.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exceptions.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/yacc.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/lex.prf:
hairless-midiserial.pro:
resources.qrc:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Resources/QtWidgets.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Resources/QtGui.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Resources/QtCore.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile hairless-midiserial.pro CONFIG+=sdk_no_version_check

qmake_all: FORCE

hairless-midiserial.app/Contents/PkgInfo: 
	@test -d hairless-midiserial.app/Contents || mkdir -p hairless-midiserial.app/Contents
	@$(DEL_FILE) hairless-midiserial.app/Contents/PkgInfo
	@echo "APPL????" > hairless-midiserial.app/Contents/PkgInfo
hairless-midiserial.app/Contents/Resources/empty.lproj: 
	@test -d hairless-midiserial.app/Contents/Resources || mkdir -p hairless-midiserial.app/Contents/Resources
	@touch hairless-midiserial.app/Contents/Resources/empty.lproj
	
hairless-midiserial.app/Contents/Info.plist: 
	@test -d hairless-midiserial.app/Contents || mkdir -p hairless-midiserial.app/Contents
	@$(DEL_FILE) hairless-midiserial.app/Contents/Info.plist
	@sed -e "s,@SHORT_VERSION@,1.0,g" -e "s,\$${QMAKE_SHORT_VERSION},1.0,g" -e "s,@FULL_VERSION@,1.0.0,g" -e "s,\$${QMAKE_FULL_VERSION},1.0.0,g" -e "s,@TYPEINFO@,????,g" -e "s,\$${QMAKE_PKGINFO_TYPEINFO},????,g" -e "s,@BUNDLEIDENTIFIER@,com.yourcompany.hairless-midiserial,g" -e "s,\$${PRODUCT_BUNDLE_IDENTIFIER},com.yourcompany.hairless-midiserial,g" -e "s,\$${MACOSX_DEPLOYMENT_TARGET},10.13,g" -e "s,\$${IPHONEOS_DEPLOYMENT_TARGET},,g" -e "s,\$${TVOS_DEPLOYMENT_TARGET},,g" -e "s,\$${WATCHOS_DEPLOYMENT_TARGET},,g" -e "s,@ICON@,icon.icns,g" -e "s,\$${ASSETCATALOG_COMPILER_APPICON_NAME},icon.icns,g" -e "s,@EXECUTABLE@,hairless-midiserial,g" -e "s,@LIBRARY@,hairless-midiserial,g" -e "s,\$${EXECUTABLE_NAME},hairless-midiserial,g" -e "s,@TYPEINFO@,????,g" -e "s,\$${QMAKE_PKGINFO_TYPEINFO},????,g" /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/Info.plist.app >hairless-midiserial.app/Contents/Info.plist
hairless-midiserial.app/Contents/Resources/icon.icns: images/icon.icns
	@test -d hairless-midiserial.app/Contents/Resources/ || mkdir -p hairless-midiserial.app/Contents/Resources/
	@$(DEL_FILE) hairless-midiserial.app/Contents/Resources/icon.icns
	@$(COPY_FILE) images/icon.icns hairless-midiserial.app/Contents/Resources/icon.icns

all: Makefile \
		hairless-midiserial.app/Contents/PkgInfo \
		hairless-midiserial.app/Contents/Resources/empty.lproj \
		hairless-midiserial.app/Contents/Info.plist \
		hairless-midiserial.app/Contents/Resources/icon.icns hairless-midiserial.app/Contents/MacOS/hairless-midiserial

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents resources.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents src/Bridge.h src/Settings.h src/BlinkenLight.h src/ui/mainwindow.h src/ui/settingsdialog.h src/ui/aboutdialog.h libraries/qextserialport/src/qextserialport.h libraries/qextserialport/src/qextserialenumerator.h libraries/qextserialport/src/qextserialport_global.h libraries/rtmidi/RtMidi.h src/QRtMidiIn.h src/PortLatency.h $(DISTDIR)/
	$(COPY_FILE) --parents src/main.cpp src/Bridge.cpp src/BlinkenLight.cpp src/ui/mainwindow.cpp src/ui/aboutdialog.cpp src/ui/settingsdialog.cpp libraries/qextserialport/src/qextserialport.cpp libraries/qextserialport/src/qextserialenumerator.cpp libraries/qextserialport/src/qextserialport_unix.cpp libraries/qextserialport/src/qextserialenumerator_osx.cpp libraries/rtmidi/RtMidi.cpp src/QRtMidiIn.cpp src/PortLatency.cpp src/PortLatency_osx.cpp $(DISTDIR)/
	$(COPY_FILE) --parents src/ui/mainwindow.ui src/ui/settingsdialog.ui src/ui/aboutdialog.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) -r hairless-midiserial.app
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

xcodeproj:
	@$(QMAKE) -spec macx-xcode "$(EXPORT__PRO_FILE_)" CONFIG+=sdk_no_version_check

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_resources.cpp
qrc_resources.cpp: resources.qrc \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/rcc \
		images/led-on.png \
		images/arrows.png \
		images/logo.png \
		images/led-off.png \
		images/icon48.png
	/usr/local/Cellar/qt@5/5.15.16_2/bin/rcc -name resources resources.qrc -o qrc_resources.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/data/dummy.cpp
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ -pipe -stdlib=libc++ -O2 $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -mmacosx-version-min=10.13 -Wall -Wextra -dM -E -o moc_predefs.h /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_Bridge.cpp moc_BlinkenLight.cpp moc_mainwindow.cpp moc_settingsdialog.cpp moc_aboutdialog.cpp moc_qextserialport.cpp moc_qextserialenumerator.cpp moc_QRtMidiIn.cpp moc_PortLatency.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_Bridge.cpp moc_BlinkenLight.cpp moc_mainwindow.cpp moc_settingsdialog.cpp moc_aboutdialog.cpp moc_qextserialport.cpp moc_qextserialenumerator.cpp moc_QRtMidiIn.cpp moc_PortLatency.cpp
moc_Bridge.cpp: src/Bridge.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QThread \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qthread.h \
		libraries/rtmidi/RtMidi.h \
		src/QRtMidiIn.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/Bridge.h -o moc_Bridge.cpp

moc_BlinkenLight.cpp: src/BlinkenLight.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QLabel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qlabel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/BlinkenLight.h -o moc_BlinkenLight.cpp

moc_mainwindow.cpp: src/ui/mainwindow.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QMainWindow \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qmainwindow.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QComboBox \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qcombobox.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QLabel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qlabel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QMenuBar \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qmenubar.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		libraries/rtmidi/RtMidi.h \
		src/Bridge.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QThread \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qthread.h \
		src/QRtMidiIn.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/ui/mainwindow.h -o moc_mainwindow.cpp

moc_settingsdialog.cpp: src/ui/settingsdialog.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QDialog \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qdialog.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/ui/settingsdialog.h -o moc_settingsdialog.cpp

moc_aboutdialog.cpp: src/ui/aboutdialog.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QDialog \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qdialog.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/ui/aboutdialog.h -o moc_aboutdialog.cpp

moc_qextserialport.cpp: libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib libraries/qextserialport/src/qextserialport.h -o moc_qextserialport.cpp

moc_qextserialenumerator.cpp: libraries/qextserialport/src/qextserialenumerator.h \
		libraries/qextserialport/src/qextserialport_global.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib libraries/qextserialport/src/qextserialenumerator.h -o moc_qextserialenumerator.cpp

moc_QRtMidiIn.cpp: src/QRtMidiIn.h \
		libraries/rtmidi/RtMidi.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/QRtMidiIn.h -o moc_QRtMidiIn.cpp

moc_PortLatency.cpp: src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/dev/hairless-midiserial-Qt5/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/dev/hairless-midiserial-Qt5 -I/Users/<USER>/dev/hairless-midiserial-Qt5/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/qextserialport/src -I/Users/<USER>/dev/hairless-midiserial-Qt5/libraries/rtmidi -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/PortLatency.h -o moc_PortLatency.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h ui_settingsdialog.h ui_aboutdialog.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h ui_settingsdialog.h ui_aboutdialog.h
ui_mainwindow.h: src/ui/mainwindow.ui \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/uic \
		src/BlinkenLight.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QLabel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qlabel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h
	/usr/local/Cellar/qt@5/5.15.16_2/bin/uic src/ui/mainwindow.ui -o ui_mainwindow.h

ui_settingsdialog.h: src/ui/settingsdialog.ui \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/uic
	/usr/local/Cellar/qt@5/5.15.16_2/bin/uic src/ui/settingsdialog.ui -o ui_settingsdialog.h

ui_aboutdialog.h: src/ui/aboutdialog.ui \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/uic
	/usr/local/Cellar/qt@5/5.15.16_2/bin/uic src/ui/aboutdialog.ui -o ui_aboutdialog.h

compiler_rez_source_make_all:
compiler_rez_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: src/main.cpp src/ui/mainwindow.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QMainWindow \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qmainwindow.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QComboBox \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qcombobox.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QLabel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qlabel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QMenuBar \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qmenubar.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		libraries/rtmidi/RtMidi.h \
		src/Bridge.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QThread \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qthread.h \
		src/QRtMidiIn.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o src/main.cpp

Bridge.o: src/Bridge.cpp src/Bridge.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QThread \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qthread.h \
		libraries/rtmidi/RtMidi.h \
		src/QRtMidiIn.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		libraries/qextserialport/src/qextserialenumerator.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVector \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvector.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QIODevice \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qiodevice.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o Bridge.o src/Bridge.cpp

BlinkenLight.o: src/BlinkenLight.cpp src/BlinkenLight.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QLabel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qlabel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/QPixmapCache \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/qpixmapcache.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o BlinkenLight.o src/BlinkenLight.cpp

mainwindow.o: src/ui/mainwindow.cpp src/ui/mainwindow.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QMainWindow \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qmainwindow.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QComboBox \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qcombobox.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QLabel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qlabel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QMenuBar \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qmenubar.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		libraries/rtmidi/RtMidi.h \
		src/Bridge.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QThread \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qthread.h \
		src/QRtMidiIn.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		ui_mainwindow.h \
		libraries/qextserialport/src/qextserialenumerator.h \
		src/Settings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		src/ui/settingsdialog.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QDialog \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qdialog.h \
		src/ui/aboutdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o src/ui/mainwindow.cpp

aboutdialog.o: src/ui/aboutdialog.cpp src/ui/aboutdialog.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QDialog \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qdialog.h \
		ui_aboutdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o aboutdialog.o src/ui/aboutdialog.cpp

settingsdialog.o: src/ui/settingsdialog.cpp src/ui/settingsdialog.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/QDialog \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtWidgets.framework/Headers/qdialog.h \
		ui_settingsdialog.h \
		src/Settings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o settingsdialog.o src/ui/settingsdialog.cpp

qextserialport.o: libraries/qextserialport/src/qextserialport.cpp libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		libraries/qextserialport/src/qextserialport_p.h \
		moc_qextserialport.cpp
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qextserialport.o libraries/qextserialport/src/qextserialport.cpp

qextserialenumerator.o: libraries/qextserialport/src/qextserialenumerator.cpp libraries/qextserialport/src/qextserialenumerator.h \
		libraries/qextserialport/src/qextserialport_global.h \
		libraries/qextserialport/src/qextserialenumerator_p.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSocketNotifier \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsocketnotifier.h \
		moc_qextserialenumerator.cpp
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qextserialenumerator.o libraries/qextserialport/src/qextserialenumerator.cpp

qextserialport_unix.o: libraries/qextserialport/src/qextserialport_unix.cpp libraries/qextserialport/src/qextserialport.h \
		libraries/qextserialport/src/qextserialport_global.h \
		libraries/qextserialport/src/qextserialport_p.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qextserialport_unix.o libraries/qextserialport/src/qextserialport_unix.cpp

qextserialenumerator_osx.o: libraries/qextserialport/src/qextserialenumerator_osx.cpp libraries/qextserialport/src/qextserialenumerator.h \
		libraries/qextserialport/src/qextserialport_global.h \
		libraries/qextserialport/src/qextserialenumerator_p.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSocketNotifier \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsocketnotifier.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qextserialenumerator_osx.o libraries/qextserialport/src/qextserialenumerator_osx.cpp

RtMidi.o: libraries/rtmidi/RtMidi.cpp libraries/rtmidi/RtMidi.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o RtMidi.o libraries/rtmidi/RtMidi.cpp

QRtMidiIn.o: src/QRtMidiIn.cpp src/QRtMidiIn.h \
		libraries/rtmidi/RtMidi.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QByteArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qbytearray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o QRtMidiIn.o src/QRtMidiIn.cpp

PortLatency.o: src/PortLatency.cpp src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PortLatency.o src/PortLatency.cpp

PortLatency_osx.o: src/PortLatency_osx.cpp src/PortLatency.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PortLatency_osx.o src/PortLatency_osx.cpp

qrc_resources.o: qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_resources.o qrc_resources.cpp

moc_Bridge.o: moc_Bridge.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_Bridge.o moc_Bridge.cpp

moc_BlinkenLight.o: moc_BlinkenLight.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_BlinkenLight.o moc_BlinkenLight.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

moc_settingsdialog.o: moc_settingsdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_settingsdialog.o moc_settingsdialog.cpp

moc_aboutdialog.o: moc_aboutdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_aboutdialog.o moc_aboutdialog.cpp

moc_QRtMidiIn.o: moc_QRtMidiIn.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_QRtMidiIn.o moc_QRtMidiIn.cpp

moc_PortLatency.o: moc_PortLatency.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PortLatency.o moc_PortLatency.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

